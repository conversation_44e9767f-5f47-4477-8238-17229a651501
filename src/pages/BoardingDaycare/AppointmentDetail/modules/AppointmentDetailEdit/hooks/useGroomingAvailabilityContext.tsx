import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { atom, useAtom } from 'jotai';
import { createContext, useContext, type PropsWithChildren } from 'react';
import { type UnavailableServiceInfo } from '../../../../components/PartialNoAvailabilityModal';
import { type CheckGroomingServiceAvailabilityResult } from './useCheckGroomingServiceAvailability';

export interface AppointmentEditSharedState extends CheckGroomingServiceAvailabilityResult {}

const defaultSharedState: AppointmentEditSharedState = {
  requestOutputList: [],
  serviceDetailKeyMapToResponse: {},
  getPetServiceAvailableDates: () => [],
  getPetServiceHasAvailability: () => false,
  petIdMapToUnavailableServiceDetailKeyList: {},
  additionalGroomingPetAndServiceDetailMap: {},
  hasUnavailableGroomingServices: false,
  unavailableServices: [],
  removedServiceDetailKeys: [],
};

export const appointmentEditSharedStateAtom = atom<AppointmentEditSharedState>(defaultSharedState);

// 重置 atom，用于页面重新进入时清理状态
export const resetAppointmentEditSharedStateAtom = atom(null, (_get, set) => {
  set(appointmentEditSharedStateAtom, defaultSharedState);
});

interface GroomingAvailabilityContextValue {
  getAvailableDates: (serviceDetailKey: string) => string[];
  setAvailabilityData: (data: AppointmentEditSharedState) => void;

  getRemovedServiceDetailKeys: () => string[];
  getUnavailableServices: () => UnavailableServiceInfo[];

  resetSharedState: () => void;
}

const GroomingAvailabilityContext = createContext<GroomingAvailabilityContextValue>({
  getAvailableDates: () => [],
  setAvailabilityData: () => {},
  getRemovedServiceDetailKeys: () => [],
  getUnavailableServices: () => [],
  resetSharedState: () => {},
});

export const useGroomingAvailabilityContext = () => useContext(GroomingAvailabilityContext);

export const GroomingAvailabilityProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [sharedState, setSharedState] = useAtom(appointmentEditSharedStateAtom);

  // 原有的 grooming availability 相关方法
  const getAvailableDates = useLatestCallback((serviceDetailKey: string) => {
    return sharedState.getPetServiceAvailableDates(serviceDetailKey) || [];
  });

  const setAvailabilityData = useLatestCallback((data: AppointmentEditSharedState) => {
    setSharedState((prev) => ({
      ...prev,
      ...data,
    }));
  });

  // removedServiceDetailKeys 相关方法
  const getRemovedServiceDetailKeys = useLatestCallback(() => {
    return sharedState.removedServiceDetailKeys;
  });

  // unavailableServices 相关方法
  const getUnavailableServices = useLatestCallback(() => {
    return sharedState.unavailableServices;
  });

  // 重置状态方法
  const resetSharedState = useLatestCallback(() => {
    setSharedState(defaultSharedState);
  });

  const contextValue = {
    getAvailableDates,
    setAvailabilityData,
    getRemovedServiceDetailKeys,
    getUnavailableServices,
    resetSharedState,
  };

  return <GroomingAvailabilityContext.Provider value={contextValue}>{children}</GroomingAvailabilityContext.Provider>;
};
