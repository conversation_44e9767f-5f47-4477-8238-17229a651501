import { OperationSection, type OperationSectionProps } from 'components/Layout/OperationSection';
import { usePetInfoForm } from 'hooks/usePetInfoForm';
import { PetFullForm } from 'pages/Pet/PetFullForm/PetFullForm';
import { usePetStore } from 'petFactory/petStore';
import { memo, type PropsWithChildren } from 'react';
import { buildPet, flattenPet } from 'utils/pet';
import { useAdditionalInfoTransformer } from 'utils/question';
import { Button } from 'widgets/Button/Button';

interface AddPetProps extends Omit<OperationSectionProps, 'onClose'> {
  onClose: (res?: number) => void;
}

export const AddPet = memo((props: PropsWithChildren<AddPetProps>) => {
  const { show, children, onClose } = props;
  const { petStore } = usePetStore();
  const additionalInfoTransformer = useAdditionalInfoTransformer(true);
  const { inputsRef, petTypeIdRef, typeSelectorRef, additionalFormRef, vaccineListRef, medicalFormRef, onSubmit } =
    usePetInfoForm();

  return (
    <>
      <OperationSection
        show={show}
        className="z-[1] max-h-[90vh] web:mx-[var(--web-mx)] !pt-0"
        transitionDelay={0}
        portal
        mask
        headerSticky
        title="Add a pet"
        onClose={() => onClose()}
      >
        <div className="flex flex-col text-left mx-[-20px] web:mx-[-32px] mb-[100px] mt-[-20px]">
          <PetFullForm
            basicFormRefs={{
              inputsRef,
              petTypeIdRef,
              typeSelectorRef,
            }}
            additionalFormRef={additionalFormRef}
            medicalFormRef={medicalFormRef}
            vaccineListRef={vaccineListRef}
            disabledBasic={false}
            isExistingPet={false}
            useBusinessPetTypes={false}
          />
          {children}
        </div>
      </OperationSection>

      <OperationSection
        portal
        show={show}
        className="z-[1] bg-opacity-0 !pt-0 web:mx-[var(--web-mx)] pointer-events-none child:pointer-events-auto"
        transitionDelay={150}
      >
        <Button
          className="btn-moe-large btn btn-primary w-full"
          onClick={async () => {
            await onSubmit(async (newVal) => {
              const newPetInfo = buildPet({
                ...newVal,
                additionalInfoTransformer,
              });
              const res = await petStore.create(flattenPet(newPetInfo));
              onClose(res);
            });
          }}
        >
          Add pet
        </Button>
      </OperationSection>
    </>
  );
});
