import { OperationSection, type OperationSectionProps } from 'components/Layout/OperationSection';
import { usePetInfoForm } from 'hooks/usePetInfoForm';
import { useGetPetInfo } from 'pages/Pet/hooks/usePet';
import { useUpdatePet } from 'pages/Pet/hooks/useUpdatePet';
import { useGetPetDefaultValues } from 'pages/Pet/hooks/useUpdateQuestion';
import { PetFullForm } from 'pages/Pet/PetFullForm/PetFullForm';
import { memo, useMemo } from 'react';
import { Button } from 'widgets/Button/Button';

interface EditPetProps extends OperationSectionProps {
  petId: number;
}

export const EditPet = memo((props: EditPetProps) => {
  const { petId, onClose } = props;
  const show = Boolean(petId);

  const getAllDefaultValues = useGetPetDefaultValues();
  const getPetInfo = useGetPetInfo();
  const updatePet = useUpdatePet();

  const currentPetInfo = getPetInfo(petId);
  const isExistingPet = !currentPetInfo?.isNewPet;
  const defaultValues = useMemo(() => getAllDefaultValues(currentPetInfo || {}), [currentPetInfo]);

  const { inputsRef, petTypeIdRef, typeSelectorRef, additionalFormRef, medicalFormRef, vaccineListRef, onSubmit } =
    usePetInfoForm();

  return (
    <>
      <OperationSection
        show={show}
        className="z-[1] max-h-[90vh] web:mx-[var(--web-mx)] !pt-0"
        transitionDelay={0}
        portal
        mask
        headerSticky
        title="Edit pet"
        onClose={onClose}
      >
        <div className="flex flex-col text-left mx-[-20px] web:mx-[-32px] mb-[100px] mt-[-20px]">
          <PetFullForm
            basicFormRefs={{
              inputsRef,
              petTypeIdRef,
              typeSelectorRef,
            }}
            basicFormDefaultValues={defaultValues.basic}
            additionalFormRef={additionalFormRef}
            additionalFormDefaultValues={defaultValues.additional}
            medicalFormRef={medicalFormRef}
            vaccineListRef={vaccineListRef}
            disabledBasic={isExistingPet}
            isExistingPet={isExistingPet}
            useBusinessPetTypes={false}
          />
        </div>
      </OperationSection>

      <OperationSection
        portal
        show={show}
        className="z-[1] bg-opacity-0 !pt-0 web:mx-[var(--web-mx)] pointer-events-none child:pointer-events-auto"
        transitionDelay={150}
      >
        <Button
          className="btn-moe-large btn btn-primary w-full"
          onClick={async () => {
            await onSubmit(async (newVal) => {
              await updatePet(petId, newVal);
              onClose?.();
            });
          }}
        >
          Save
        </Button>
      </OperationSection>
    </>
  );
});
