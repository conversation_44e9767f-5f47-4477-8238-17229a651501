import { ExistingPetAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { Breed } from '@moego/client-lib-components/dist/Form/fields/Breed';
import { useAtomValueRef } from '@moego/client-lib-jotai/dist/useAtomRef';
import { type InputController } from '@moego/client-lib-widgets/dist/Input/hooks/useInput';
import classNames from 'classnames';
import { CoatType } from 'components/Form/fields/CoatType';
import { WeightInput } from 'components/Form/fields/Weight';
import { isEmpty } from 'lodash';
import {
  forwardRef,
  memo,
  useEffect,
  useMemo,
  useState,
  type ForwardedRef,
  type MutableRefObject,
  type ReactElement,
  type RefObject,
} from 'react';
import { usePetEntryAcceptedQuestions } from 'state/question/hooks/usePetEntryAcceptedQuestions';
import { bookingPetOptionsState } from 'state/question/state';
import { type BookingQuestionEntity } from 'types/entity';
import { type NewPet } from 'types/pet';
import { PetQuestionKey } from 'types/question';
import { cn } from 'utils/classNames';
import { getQuestionExtraJson } from 'utils/question';
import { Condition } from 'widgets/Condition';
import { Input, useInput, useMultipleInputs, type MultipleInputController } from 'widgets/Input/Input';
import { PetTypeSelector, type PetTypeId } from './PetTypeSelector';
import { type PetFormFocusInfo } from './usePetFormFocus';

const formItemClassName = 'px-0 mx-[20px] web:mx-[32px]';

export interface PetBasicFormProps extends Pick<PetFormFocusInfo, 'isFormFocusOnField' | 'setFormFocus'> {
  inputsRef: MutableRefObject<MultipleInputController | null>;
  petTypeIdRef: MutableRefObject<number | undefined>;
  typeSelectorRef: RefObject<HTMLDivElement>;
  defaultValues?: Partial<NewPet>;
  className?: string;
  children?: ReactElement;
  disabledBasic?: boolean;
  onPetTypeChange?: (petType: number) => void;
  /** 是否在编辑已存在的 pet，和 new pet 会用不一样的 question list */
  isExistingPet: boolean;
  /** 是否使用 business 维度的 petType 选项，默认是消费了 booking flow 的 availability pet type 的配置 */
  useBusinessPetTypes?: boolean;
}

const isFieldViewOnly = (question: Partial<BookingQuestionEntity>, isExistingPet: boolean) => {
  return isExistingPet && question.existingPetAccessMode === ExistingPetAccessMode.VIEW;
};

/**
 * 需要注意 Booking flow 和 portal 两种场景的数据依赖
 */
const PetBasicFormComponent = (props: PetBasicFormProps, ref?: ForwardedRef<HTMLDivElement>) => {
  const {
    inputsRef,
    petTypeIdRef,
    typeSelectorRef,
    defaultValues,
    className,
    children,
    isFormFocusOnField,
    setFormFocus,
    onPetTypeChange,
    isExistingPet,
    useBusinessPetTypes,
  } = props;

  const bookingPetQuestions = usePetEntryAcceptedQuestions(isExistingPet);

  // 合并所有问题的查找逻辑
  const questions = useMemo(() => {
    const questionMap = {
      weight: bookingPetQuestions.find((i) => i.key === PetQuestionKey.Weight),
      coat: bookingPetQuestions.find((i) => i.key === PetQuestionKey.CoatType),
      name: bookingPetQuestions.find((i) => i.key === PetQuestionKey.PetName),
      breed: bookingPetQuestions.find((i) => i.key === PetQuestionKey.Breed),
      type: bookingPetQuestions.find((i) => i.key === PetQuestionKey.PetType),
    };

    return {
      weight: questionMap.weight || {},
      coat: questionMap.coat || {},
      name: questionMap.name || {},
      breed: questionMap.breed || {},
      type: questionMap.type || {},
    };
  }, [bookingPetQuestions]);

  // 提取问题配置
  const questionConfig = useMemo(() => {
    const createFieldConfig = (question: Partial<BookingQuestionEntity>) => {
      const viewOnly = !isEmpty(question) && isFieldViewOnly(question, isExistingPet);
      return {
        question,
        // 设计交互逻辑： 如果问题为只读，则不显示必填标识
        isRequired: viewOnly ? false : !!(question as Partial<BookingQuestionEntity>).isRequired,
        needShow: !isEmpty(question),
        viewOnly,
      };
    };

    const { weight, coat, name, breed, type } = questions;

    return {
      weight: createFieldConfig(weight),
      coat: {
        ...createFieldConfig(coat),
        extraJson: (coat as Partial<BookingQuestionEntity>).extraJson,
      },
      name: createFieldConfig(name),
      breed: createFieldConfig(breed),
      type: createFieldConfig(type),
    };
  }, [questions, isExistingPet]);

  const coatReference = useMemo(
    () => getQuestionExtraJson<{ referencePicture: string }>(questionConfig.coat.extraJson).referencePicture,
    [questionConfig.coat.extraJson],
  );

  const { breeds } = useAtomValueRef(bookingPetOptionsState).current.data ?? {};
  const [petTypeId, setPetTypeId] = useState<PetTypeId>(defaultValues?.petTypeId);

  // 创建输入控件
  const breedInput = useInput(defaultValues?.breed, { lazyValidate: true });
  const nameInput = useInput(defaultValues?.petName, { lazyValidate: true });
  const weightInput = useInput(defaultValues?.weight, { lazyValidate: true });
  const coatInput = useInput(defaultValues?.coat, { lazyValidate: true });

  // 根据配置决定哪些输入需要包含在验证中
  const inputs = useMultipleInputs(
    questionConfig.breed.viewOnly || !questionConfig.breed.needShow ? undefined : breedInput,
    questionConfig.name.viewOnly || !questionConfig.name.needShow ? undefined : nameInput,
    questionConfig.weight.viewOnly || !questionConfig.weight.needShow ? undefined : weightInput,
    questionConfig.coat.viewOnly || !questionConfig.coat.needShow ? undefined : coatInput,
  );

  const isDropDownFocus = isFormFocusOnField('coat') || isFormFocusOnField('breed');

  useEffect(() => {
    inputsRef.current = inputs;
  }, [inputsRef, inputs]);

  useEffect(() => {
    petTypeIdRef.current = petTypeId;
  }, [petTypeIdRef, petTypeId]);

  const handlePetTypeChange = (newPetTypeId: PetTypeId) => {
    setPetTypeId(newPetTypeId);
    onPetTypeChange?.(newPetTypeId!);
  };

  return (
    <div
      ref={ref}
      className={classNames(
        'px-0 child:mt-[16px] web:child:px-[32px] bg-white',
        // 选择coat / breed 的时候，让表单在 提交按钮 之上，否则coat的可选项options，可能会被按钮遮住
        isDropDownFocus && 'relative z-[2]',
        className,
      )}
      data-input-role="root-container"
    >
      <Condition if={questionConfig.type.needShow}>
        <PetTypeSelector
          ref={typeSelectorRef}
          onChange={handlePetTypeChange}
          defaultValue={petTypeId}
          showRequiredIndicator={!!questionConfig.type.isRequired}
          readOnly={questionConfig.type.viewOnly}
          useBusinessPetTypes={useBusinessPetTypes}
        />
      </Condition>

      <Condition if={questionConfig.breed.needShow}>
        <div
          className={cn({
            'cursor-not-allowed': questionConfig.breed.viewOnly,
            'breed-readonly-wrapper': questionConfig.breed.viewOnly, // 添加自定义类名
          })}
        >
          <Breed
            className={cn(formItemClassName, 'mx-0 web:mx-0', {
              'pointer-events-none': questionConfig.breed.viewOnly,
            })}
            options={breeds}
            petTypeId={petTypeId!}
            input={breedInput as InputController<string | number>}
            onFocusChange={(focus: boolean) => setFormFocus('breed', focus)}
          />
        </div>
      </Condition>

      <Condition if={questionConfig.name.needShow}>
        <Input
          className={classNames('input-moe', formItemClassName)}
          input={nameInput}
          label="Name"
          maxLength={50}
          validator="notnull"
          onFocus={() => setFormFocus('name', true)}
          onBlur={() => setFormFocus('name', false)}
          showRequiredIndicator={!!questionConfig.name.isRequired}
          autoScrollIntoView
        />
      </Condition>

      <Condition if={questionConfig.weight.needShow}>
        <WeightInput
          input={weightInput}
          label="Weight"
          className={classNames('input-moe', formItemClassName)}
          required={!!questionConfig.weight.isRequired}
          onFocus={() => setFormFocus('weight', true)}
          onBlur={() => setFormFocus('weight', false)}
          readOnly={questionConfig.weight.viewOnly}
          autoScrollIntoView
          inputClassName={cn({
            '!bg-[#F3F3F3]': questionConfig.weight.viewOnly,
          })}
          suffixClassName={cn({
            '!text-[#828282]': questionConfig.weight.viewOnly,
          })}
        />
      </Condition>

      <Condition if={questionConfig.coat.needShow}>
        <div
          className={cn({
            'cursor-not-allowed': questionConfig.coat.viewOnly,
          })}
        >
          <CoatType
            className={cn(formItemClassName, 'mx-0 web:mx-0')}
            input={coatInput as InputController}
            required={!!questionConfig.coat.isRequired}
            onFocusChange={(focus: boolean) => setFormFocus('coat', focus)}
            referenceUrl={coatReference}
            readOnly={questionConfig.coat.viewOnly}
          />
        </div>
      </Condition>
      {children}
    </div>
  );
};

export const PetBasicForm = memo(forwardRef(PetBasicFormComponent));
