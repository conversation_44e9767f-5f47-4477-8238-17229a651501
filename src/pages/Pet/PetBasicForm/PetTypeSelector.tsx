import { useAtomValueRef } from '@moego/client-lib-jotai/dist/useAtomRef';
import { TabSelector } from '@moego/client-lib-widgets/dist/Selector/TabSelector';
import { default as classNames } from 'classnames';
import { forwardRef, memo, useCallback, useMemo, type ForwardedRef } from 'react';
import { businessAvailablePetTypeInfoState } from 'state/business/state';
import { bookingPetOptionsState } from 'state/question/state';
import { type PetTypeEntity } from 'types/entity';
import { cn } from 'utils/classNames';

export type PetTypeId = PetTypeEntity['petTypeId'];

export interface PetTypeSelectorProps {
  onChange: (v: PetTypeId) => unknown;
  defaultValue?: PetTypeId;
  showRequiredIndicator?: boolean;
  readOnly?: boolean;
  /** 是否使用 business 维度的 petType 选项，默认是消费了 booking flow 的 availability pet type 的配置 */
  useBusinessPetTypes?: boolean;
}

function PetTypeSelectorComponent(
  { onChange, defaultValue, showRequiredIndicator, readOnly, useBusinessPetTypes }: PetTypeSelectorProps,
  ref: ForwardedRef<HTMLDivElement>,
) {
  const { petTypeList: bookingFlowPetTypeList = [] } = useAtomValueRef(bookingPetOptionsState).current.data ?? {};
  const businessPetTypeList = useAtomValueRef(businessAvailablePetTypeInfoState).current?.data ?? [];
  const petTypeList = useBusinessPetTypes ? businessPetTypeList : bookingFlowPetTypeList;
  const options = useMemo<PetTypeEntity[]>(
    () => petTypeList.filter((i) => i.bookOnlineAvailable && (readOnly ? i.petTypeId === defaultValue : true)),
    [petTypeList, readOnly, defaultValue],
  );
  const defaultOption = useMemo(
    () => options.find((i) => i.petTypeId === defaultValue) || options[0],
    [options, defaultValue],
  );
  const onSelectorChange = useCallback(({ petTypeId }: PetTypeEntity) => onChange(petTypeId), [onChange]);

  return (
    <div ref={ref} className={classNames('form-control relative px-0')}>
      <TabSelector
        options={options}
        defaultValue={defaultOption}
        onChange={onSelectorChange}
        renderKey="typeName"
        labelClassName="mx-[20px] web:mx-[32px]"
        optionsClassName={cn('px-[20px] flex-wrap web:px-[32px]', {
          'cursor-not-allowed': readOnly,
        })}
        itemWrapperClassName={cn({
          'pointer-events-none': readOnly,
        })}
        activatedItemClassName={cn({
          'bg-[#F3F3F3] text-[#505050] border-secondary-line': readOnly,
        })}
        itemClassName="min-w-[80px] !h-[40px] px-[25px] mb-[6px] mr-[12px]"
        label="Type"
        showRequiredIndicator={showRequiredIndicator}
      />
    </div>
  );
}

export const PetTypeSelector = memo(forwardRef(PetTypeSelectorComponent));
