import { FormContext } from '@moego/client-lib-components/dist/Form/context';
import { VaccineList, type VaccineListRef } from '@moego/client-lib-components/dist/Vaccine/VaccineList';
import { type PetVaccineRecord } from '@moego/client-lib-types/dist/pet';
import { Partition } from '@moego/client-lib-widgets/dist/Layout/Partition';
import { useAtomValue } from 'jotai';
import { forwardRef, memo, useMemo, useState, type ForwardedRef, type MutableRefObject } from 'react';
import { bdServiceItemTypeDerivedAtom } from 'state/boardingDaycare/serviceItemTypeAtom';
import { usePetEntryAcceptedQuestions } from 'state/question/hooks/usePetEntryAcceptedQuestions';
import { PetQuestionKey } from 'types/question';
import { cn } from 'utils/classNames';
import { getAdditionalPetQuestions, getMedicalPetQuestions, medicalFormKeys, vaccineFormKeys } from 'utils/question';
import { useGetVaccines } from '../hooks/useVaccine';
import { useVaccineContext } from '../hooks/useVaccineContext';
import {
  AdditionalPetFormType,
  PetAdditionalForm,
  type PetAdditionalFormProps,
} from '../PetAdditionalForm/PetAdditionalForm';
import { PetBasicForm, type PetBasicFormProps } from '../PetBasicForm/PetBasicForm';
import { usePetFormFocus } from '../PetBasicForm/usePetFormFocus';

export interface PetFullFormProps {
  basicFormRefs: Pick<PetBasicFormProps, 'inputsRef' | 'petTypeIdRef' | 'typeSelectorRef'>;
  basicFormDefaultValues?: PetBasicFormProps['defaultValues'];
  additionalFormRef: PetAdditionalFormProps['formRef'];
  medicalFormRef: PetAdditionalFormProps['formRef'];
  additionalFormDefaultValues?: PetAdditionalFormProps['defaultValues'];
  vaccineListRef: MutableRefObject<VaccineListRef | null>;
  disabledBasic?: boolean;
  /** 是否在编辑已存在的 pet，和 new pet 会用不一样的 question list */
  isExistingPet: boolean;
  hideVaccineQuestion?: boolean;
  hideMedicalQuestion?: boolean;
  hideGeneralTitle?: boolean;
  hideCustomizeQuestion?: boolean;
  hideDefaultVaccineValue?: boolean;
  /** 是否使用 business 维度的 petType 选项，默认是消费了 booking flow 的 availability pet type 的配置 */
  useBusinessPetTypes?: boolean;
}

function PetFullFormComponent(
  {
    basicFormRefs,
    basicFormDefaultValues,
    additionalFormRef,
    medicalFormRef,
    additionalFormDefaultValues,
    vaccineListRef,
    disabledBasic,
    isExistingPet,
    hideVaccineQuestion,
    hideMedicalQuestion,
    hideGeneralTitle,
    hideCustomizeQuestion,
    hideDefaultVaccineValue,
    useBusinessPetTypes,
  }: PetFullFormProps,
  ref?: ForwardedRef<HTMLDivElement>,
) {
  // basic info 相关
  const { inputsRef, petTypeIdRef, typeSelectorRef } = basicFormRefs;
  const { setFormFocus, isFormFocusOnField } = usePetFormFocus();
  const careType = useAtomValue(bdServiceItemTypeDerivedAtom);
  const [petTypeId, setPetTypeId] = useState(basicFormDefaultValues?.petTypeId);
  // additional info 相关
  const questions = usePetEntryAcceptedQuestions(isExistingPet);
  const medicalFormQuestions = useMemo(() => getMedicalPetQuestions(questions), [questions]);
  const additionalFormQuestions = useMemo(
    () => getAdditionalPetQuestions(questions, Array.from(new Set([...medicalFormKeys, ...vaccineFormKeys]))),
    [questions],
  );

  // vaccine 相关
  const hasVaccineQuestion = !!questions?.find((i) => i?.key === PetQuestionKey.VaccineList);
  const vaccineContext = useVaccineContext(petTypeId);
  const getVaccines = useGetVaccines();
  // 此处需要 memo，以防每次 additionalFormDefaultValues 更新时，vaccines 都重新生成
  const vaccines = useMemo(() => {
    if (hideDefaultVaccineValue) return [];
    const { fullVaccines } = getVaccines(
      (additionalFormDefaultValues?.[PetQuestionKey.VaccineList] ?? []) as PetVaccineRecord[],
      careType,
      petTypeId!,
      isExistingPet,
    );
    return fullVaccines;
  }, [getVaccines, additionalFormDefaultValues?.[PetQuestionKey.VaccineList], petTypeId, careType]);

  return (
    <div
      className={cn([
        'px-0',
        {
          'mt-[16px] web:mt-[32px]': !hideGeneralTitle,
          'mt-0 web:mt-[12px]': hideGeneralTitle,
        },
      ])}
      ref={ref}
    >
      <Partition title={!hideGeneralTitle ? 'General' : ''} className="!mt-0" titleClassName="mx-[20px] web:mx-[32px]">
        <PetBasicForm
          inputsRef={inputsRef}
          petTypeIdRef={petTypeIdRef}
          typeSelectorRef={typeSelectorRef}
          defaultValues={basicFormDefaultValues}
          setFormFocus={setFormFocus}
          isFormFocusOnField={isFormFocusOnField}
          onPetTypeChange={setPetTypeId}
          disabledBasic={disabledBasic}
          isExistingPet={isExistingPet}
          useBusinessPetTypes={useBusinessPetTypes}
        />
      </Partition>
      {additionalFormQuestions?.length ? (
        <Partition className="!mt-0" contentClassName="mt-[16px]">
          <PetAdditionalForm
            formRef={additionalFormRef}
            defaultValues={additionalFormDefaultValues}
            hideVaccine
            isExistingPet={isExistingPet}
            hideCustomizeQuestion={hideCustomizeQuestion}
          />
        </Partition>
      ) : null}

      {!hideVaccineQuestion && hasVaccineQuestion ? (
        <Partition title="Vaccinations" className="mx-[20px] web:mx-[32px]" contentClassName="mt-[12px]">
          <FormContext.Provider value={vaccineContext}>
            <VaccineList ref={vaccineListRef} defaultValue={vaccines} />
          </FormContext.Provider>
        </Partition>
      ) : null}
      {!hideMedicalQuestion && medicalFormQuestions?.length ? (
        <Partition title="Medical info" titleClassName="mx-[20px] web:mx-[32px]" contentClassName="mt-[12px]">
          <PetAdditionalForm
            formRef={medicalFormRef}
            defaultValues={additionalFormDefaultValues}
            hideVaccine
            isExistingPet={isExistingPet}
            formType={AdditionalPetFormType.Medical}
          />
        </Partition>
      ) : null}
    </div>
  );
}

export const PetFullForm = memo(forwardRef(PetFullFormComponent));
