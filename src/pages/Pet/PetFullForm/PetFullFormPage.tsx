import { useVirtualKeyboardStatus } from '@moego/client-lib-hooks/dist/useVirtualKeyboardStatus';
import { withWaitingLoadableWithSkeletonScreen } from '@moego/client-lib-jotai/dist/waitingLoadable';
import { default as classNames } from 'classnames';
import { MainSection } from 'components/Layout/MainSection';
import { OperationSection } from 'components/Layout/OperationSection';
import { memo, useEffect, type ComponentType, type ReactElement } from 'react';
import { businessInfoState, businessPreferenceState } from 'state/business/state';

import { Button } from '@moego/client-lib-widgets/dist/Button';
import { usePetInfoForm, type ConfirmHandler } from 'hooks/usePetInfoForm';
import { bookingPetOptionsState, bookingPetQuestionsState } from 'state/question/state';
import { PetFullForm, type PetFullFormProps } from './PetFullForm';

export interface PetFullFormPageProps
  extends Pick<PetFullFormProps, 'basicFormDefaultValues' | 'additionalFormDefaultValues' | 'disabledBasic'> {
  title: string;
  onConfirm: {
    text: string;
    handler: ConfirmHandler;
  };
  children?: ReactElement;
  onBack?: () => void;
  /** 是否在编辑已存在的 pet，和 new pet 会用不一样的 question list */
  isExistingPet: boolean;
}

function PetFormPageComponent({
  title,
  onConfirm: { text: confirmText, handler },
  basicFormDefaultValues,
  additionalFormDefaultValues,
  disabledBasic,
  children,
  onBack,
  isExistingPet,
}: PetFullFormPageProps) {
  const { inputsRef, petTypeIdRef, typeSelectorRef, additionalFormRef, medicalFormRef, vaccineListRef, onSubmit } =
    usePetInfoForm();

  // TODO keyboardEnabled 应该需要影响 OperationSection 是否展示
  const keyboardEnabled = useVirtualKeyboardStatus(null);
  useEffect(() => {
    if (!keyboardEnabled && document.activeElement) {
      (document.activeElement as HTMLElement).blur?.();
    }
  }, [keyboardEnabled]);

  return (
    <MainSection
      title={title}
      className="h-full-compatible bg-base-100 px-0 child:px-[20px] overflow-y-scroll pb-[calc(env(safe-area-inset-bottom,0)+20px)]"
      contentClassName="web:pb-[112px]"
      backClassName="px-0"
      allowBack
      onBack={onBack}
    >
      <PetFullForm
        basicFormRefs={{
          inputsRef,
          petTypeIdRef,
          typeSelectorRef,
        }}
        basicFormDefaultValues={basicFormDefaultValues}
        additionalFormRef={additionalFormRef}
        medicalFormRef={medicalFormRef}
        additionalFormDefaultValues={additionalFormDefaultValues}
        vaccineListRef={vaccineListRef}
        disabledBasic={disabledBasic}
        isExistingPet={isExistingPet}
        useBusinessPetTypes={false}
      />
      {children}
      <div className="h-[100px]" />
      <OperationSection className="bg-transparent pointer-events-none child:pointer-events-auto">
        <Button
          className={classNames('btn btn-primary btn-moe-large w-full box-shadow')}
          onClick={async () => {
            await onSubmit(handler);
          }}
        >
          {confirmText}
        </Button>
      </OperationSection>
    </MainSection>
  );
}

export const PetFullFormPage = memo(
  withWaitingLoadableWithSkeletonScreen(
    {},
    businessPreferenceState,
    businessInfoState,
    bookingPetQuestionsState,
    bookingPetOptionsState,
  )(PetFormPageComponent as ComponentType<unknown>),
) as ComponentType<PetFullFormPageProps>;
