import { type BusinessCustomerPetModel } from '@moego/api-web/moego/models/business_customer/v1/business_customer_pet_models';
import { useActionAtom } from '@moego/client-lib-jotai/action';
import { ClientPortalTestIds } from 'configs/testIds/clientPortal';
import { useAtomValue } from 'jotai';
import { petStoreAtom } from 'petFactory/petStore';
import { memo, useRef } from 'react';
import { fetchUserPetAndVaccineAction } from 'state/pet/userPetAtom';
import { cn } from 'utils/classNames';
import { Condition } from 'widgets/Condition';
import { Icon, IconType } from 'widgets/Icon';
import { useGetMedicalInfo } from '../hooks/useGetMedicalInfo';
import { MedicalInfoEditModal, type MedicalInfoEditModalRef } from './MedicalInfoEditModal';

export interface MedicalInfoProps {
  pet: BusinessCustomerPetModel;
}

export const MedicalInfo = memo<MedicalInfoProps>((props) => {
  const { pet } = props;
  const { data: petList } = useAtomValue(petStoreAtom);
  const currentPetInfo = petList?.find((curPet) => curPet.key === Number(pet.id));
  const isExistingPet = !currentPetInfo?.isNewPet;
  const getMedicalInfo = useGetMedicalInfo(isExistingPet);
  const data = getMedicalInfo(pet);
  const medicalInfoEditModalRef = useRef<MedicalInfoEditModalRef>(null);
  const fetchUserPetAndVaccine = useActionAtom(fetchUserPetAndVaccineAction);

  const handleEdit = () => {
    medicalInfoEditModalRef.current?.show({
      id: pet.id,
      vetName: pet.vetName,
      vetPhone: pet.vetPhoneNumber,
      vetAddress: pet.vetAddress,
      healthIssues: pet.healthIssues,
    });
  };

  return (
    <Condition if={data.length}>
      <div className="mt-[20px]">
        <div className="mt-[20px] text-headline">Medical info</div>
        <div className="relative">
          <div className=" mt-[12px] p-[20px] rounded-[20px] border-[1px] border-solid border-secondary-line space-y-[16px]">
            {data.map((item, index) => {
              return (
                <div className="flex items-start child:w-[50%]" key={index}>
                  <div className="text-subhead text-text-secondary">{item.label}</div>
                  <div className="text-subhead text-text-primary flex-shrink">{item.value}</div>
                </div>
              );
            })}
          </div>
          <div
            className={cn(
              'absolute right-0 top-0 p-[10px] rounded-bl-[20px] rounded-tr-[20px] bg-secondary-line text-[#9b9b9b] cursor-pointer',
            )}
            onClick={handleEdit}
          >
            <Icon name={IconType.minorEditFilled} fontSize={20} data-testid={ClientPortalTestIds.EditPetMedicalInfo} />
          </div>
        </div>
      </div>
      <MedicalInfoEditModal
        ref={medicalInfoEditModalRef}
        onClose={fetchUserPetAndVaccine}
        isExistingPet={isExistingPet}
        pet={pet}
      />
    </Condition>
  );
});
