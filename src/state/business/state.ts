import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type AcceptCustomerType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import {
  type GetOnlineBookingScriptErrorResponse,
  type GetOnlineBookingScriptSuccessResponse,
} from '@moego/bff-openapi/dist/esm/clients/client.open-platform';
import { latLngPairToObj } from '@moego/client-lib-google-map/dist/utils';
import { getErrorMessage } from '@moego/client-lib-http/dist/http-client';
import { actionAtom } from '@moego/client-lib-jotai/dist/action';
import { loadable } from '@moego/client-lib-jotai/dist/loadable';
import { waitUntil } from '@moego/client-lib-utils/dist/promise';
import { default as dayjs, type Dayjs } from 'dayjs';
import { isToday } from 'hooks/useDateFormat';
import { atom } from 'jotai';
import { atomWithReset } from 'jotai/utils';
import { http } from 'libs/api';
import { BffPlatformClient } from 'libs/bff';
import { BookingAvailabilityServiceClient } from 'libs/clients';
import { notFoundRoute } from 'routes';
import {
  AvailabilityType,
  BookingRangeEndKind,
  BusinessType,
  type BusinessTimeFormatType,
  type IBusinessLandingComponentConfig,
} from 'types/business';
import {
  type BusinessAgreementEntity,
  type BusinessAvailablePetTypesEntity,
  type BusinessInfoEntity,
  type BusinessLocationEntity,
  type BusinessPreferenceEntity,
  type BusinessProfileDTO,
  type BusinessProfileEntity,
  type BusinessSquareInfoEntity,
  type BusinessTwilioInfoEntity,
  type LandingServiceCategoryItem,
  type StaffWorkingHoursEntity,
} from 'types/entity';
import { type OpenApiModels } from 'types/openApi/schema';
import { MAX_TIPS_AMOUNT, WEEK_DAY_NAMES } from 'utils/const';
import { getBusinessIdentifier } from 'utils/name';
import { getPetTypeOptionByEnum, type PetTypeOption } from 'utils/petType';
import { promiseWrapper } from 'utils/promise';

export const bookingNamePayload = atom(getBusinessIdentifier);
export const bookingKeyState = atom((get) => {
  const { domain, name } = get(bookingNamePayload);
  return domain ? 'domain-' + domain : 'name-' + name;
});

export enum NewClientFlowType {
  /** 正常登录 */
  NORMAL = 0,
  /** 需要询问用户 */
  QUESTION = 1,
}

export const infoRecords: {
  [bookingKey: string]: Promise<{
    businessInfo?: BusinessInfoEntity;
    squareInfo?: BusinessSquareInfoEntity;
    twilioInfo?: BusinessTwilioInfoEntity;
    availablePetTypes?: BusinessAvailablePetTypesEntity;
  }>;
} = {};

export function getBusinessInfo(key: string, payload: { name?: string; domain?: string }) {
  if (!infoRecords[key]) {
    infoRecords[key] = http.open('GET/grooming/ob/v2/client/business/info', payload);
  }
  return infoRecords[key];
}

export const businessInfoState = loadable<BusinessInfoEntity>(
  atom(async (get) => {
    const payload = get(bookingNamePayload);
    const key = get(bookingKeyState);
    if (!payload.domain && !payload.name) {
      throw new Error('Missing required parameter "domain" or "name"');
    }

    const { businessInfo } = await getBusinessInfo(key, payload);
    return businessInfo!;
  }),
  { throwError: true },
);

export const businessSquareInfoState = loadable<Partial<BusinessSquareInfoEntity> | undefined>(
  atom(async (get) => {
    const payload = get(bookingNamePayload);
    const key = get(bookingKeyState);
    if (!!payload.domain && !payload.name) {
      return;
    }
    const { squareInfo } = await getBusinessInfo(key, payload);
    // Must return a not undefined value, or it will be treated as not loaded
    return squareInfo || {};
  }),
);

export const businessTwilioInfoState = loadable<Partial<BusinessTwilioInfoEntity> | undefined>(
  atom(async (get) => {
    const payload = get(bookingNamePayload);
    const key = get(bookingKeyState);
    if (!!payload.domain && !payload.name) {
      return;
    }
    const { twilioInfo } = await getBusinessInfo(key, payload);
    return twilioInfo || {};
  }),
);

export const businessAvailablePetTypeInfoState = loadable<PetTypeOption[] | undefined>(
  atom(async (get) => {
    const payload = get(bookingNamePayload);
    const key = get(bookingKeyState);
    if (!!payload.domain && !payload.name) {
      return;
    }
    const { availablePetTypes } = await getBusinessInfo(key, payload);
    return (availablePetTypes || []).map(getPetTypeOptionByEnum);
  }),
);

export const businessLocationsState = loadable<BusinessLocationEntity[]>(
  atom(async () => {
    return http.open('GET/grooming/ob/v2/client/business/location');
  }),
);

export const businessProfileLoadable = loadable<BusinessProfileDTO>(
  atom(async () => {
    return await http.open('GET/grooming/ob/v2/client/business/profile');
  }),
  { throwError: true },
);

export const enterpriseIdState = atom((get) => {
  return get(businessProfileLoadable).data?.company.enterpriseId;
});

export const companyIdState = atom((get) => {
  return get(businessInfoState).data?.setting?.companyId;
});

export const businessProfileState = loadable<BusinessProfileEntity | undefined>(
  atom(async (get) => {
    const businessProfileData = get(businessProfileLoadable);
    const { data } = await waitUntil(
      () => Promise.resolve(businessProfileData),
      (loadableInfo) => !loadableInfo?.loading,
      100,
    );
    return data?.profile;
  }),
  { throwError: true },
);

export const businessThemeColorState = atom((get) => {
  return get(businessProfileState).data?.buttonColor;
});

export const businessPreferenceState = loadable<BusinessPreferenceEntity>(
  atom(async () => {
    return http.open('GET/grooming/ob/v2/client/business/preference');
  }),
  { throwError: true },
);

export const businessTypeState = atom((get) => {
  const preference = get(businessPreferenceState);
  const businessMode = preference.data?.businessMode;
  const isSalon = businessMode === BusinessType.GroomingSalon;
  const isMobile = businessMode === BusinessType.GroomingMobile;
  return {
    businessMode,
    isMobile,
    isSalon,
    type: isMobile ? 'mobile' : 'salon',
  };
});

export const agreementTriggerAtom = atom(false);
export const refreshAgreementsAtom = actionAtom((_get, set) => {
  set(agreementTriggerAtom, (prev) => !prev);
});

export const businessAgreementsState = loadable<BusinessAgreementEntity[]>(
  atom(async (get) => {
    get(agreementTriggerAtom);
    return http.open('GET/business/ob/v2/client/agreement');
  }),
  { throwError: true },
);

export const businessPhoneNumberState = atom((get) => {
  const businessPhoneNumber = get(businessProfileState)?.data?.phoneNumber;
  const twilioNumber = get(businessTwilioInfoState)?.data?.twilioNumber;
  return twilioNumber || businessPhoneNumber || '';
});

export const businessDateFormatState = atom((get) => {
  const { data: preference } = get(businessPreferenceState);
  if (!preference) {
    return;
  }
  const { dateFormat, dateFormatType } = preference;
  let displayDateFormat = dateFormat;
  if (dateFormatType === 3 || dateFormatType === 4) {
    displayDateFormat = dateFormat?.replace(/\./g, '/');
  } else if (dateFormatType === 6) {
    displayDateFormat = 'MM/DD/YYYY';
  }
  return { dateFormatType, dateFormat, displayDateFormat };
});

export const businessTimeFormatState = atom((get) => {
  const { data: preference } = get(businessPreferenceState);
  if (!preference) {
    return;
  }
  const { timeFormat, timeFormatType } = preference;
  return { timeFormatType: timeFormatType as BusinessTimeFormatType, timeFormat };
});

export const businessCurrencyState = atom((get) => {
  const { data: preference } = get(businessPreferenceState);
  if (!preference) {
    return { currencyCode: -1, currencySymbol: '$' };
  }
  const { currencyCode, currencySymbol } = preference;
  return { currencyCode, currencySymbol };
});

export const businessFarthestDayState = atom((get) => {
  const { bookingRangeEndType, bookingRangeEndOffset, bookingRangeEndDate } = get(businessAvailabilityInfoState);
  const dayDiff = isToday(bookingRangeEndDate!) ? 0 : dayjs(bookingRangeEndDate).diff(dayjs(), 'day') + 1;
  return bookingRangeEndType === BookingRangeEndKind.ByPresetOffset ? bookingRangeEndOffset : dayDiff;
});

export const businessLandingConfigState = loadable(
  atom(async () => {
    const [error, result] = await promiseWrapper(
      http.open('GET/grooming/ob/v2/client/business/config', {}, { stringifyError: false }),
    );
    if (error) {
      // Unpublished
      if ((error as any)?.data?.code === 40101) {
        notFoundRoute.replace({ noBack: 1 });
        return;
      } else {
        throw error;
      }
    }
    const pageComponents = result?.pageComponents;
    if (!pageComponents) {
      return;
    }

    const { gaMeasurementId, thankYouPageUrl, themeColor } = result;

    const {
      gallery,
      aboutUs,
      welcomePageMessage,
      showcase,
      businessHours,
      amenities,
      contact,
      address,
      serviceArea,
      obConfigTeams,
      obConfigClientReviews,
    } = pageComponents.reduce(
      (prev, { component, ...rest }) => ({
        ...prev,
        [component]: rest,
      }),
      {} as Partial<IBusinessLandingComponentConfig>,
    );

    return {
      thankYouPageUrl,
      gaMeasurementId,
      themeColor,
      galleryList: gallery?.galleryList?.sort((i, j) => i.sort - j.sort),
      aboutUs: aboutUs?.text,
      welcomePageMessage: welcomePageMessage?.text,
      amenities: amenities?.amenities,
      showcase,
      businessHours,
      contact,
      address,
      serviceArea: {
        data: (serviceArea?.result ?? []).map((item) => ({ ...item, polygon: item.polygon.map(latLngPairToObj) })),
        showDisplay: !!pageComponents?.find((i) => i?.component === 'serviceArea'),
      },
      obConfigTeams: obConfigTeams?.teams || [],
      obConfigClientReviews: {
        data: obConfigClientReviews?.reviews || [],
        isDisplayShowcasePhoto: !!obConfigClientReviews?.isDisplayShowcasePhoto,
      },
    };
  }),
  { throwError: true },
);

// TODO (elliot): 需要注释
export const businessWorkingHoursState = atom<{
  opening: boolean;
  openStatusLabel?: string;
  hours?: { dayOfWeek: string; hours?: string[]; rawHours?: Dayjs[][]; closedReason?: string }[];
}>((get) => {
  const config = get(businessLandingConfigState).data;
  if (!config) {
    return { opening: false, openStatusLabel: '' };
  }
  const { workingHours, closedDateList = [] } = config.businessHours || {};
  if (!workingHours) {
    return { opening: false, openStatusLabel: '' };
  }

  const todayIndex = dayjs().day();
  const weeks = WEEK_DAY_NAMES.slice(todayIndex).concat(WEEK_DAY_NAMES.slice(0, todayIndex)); // 数组顺序从今天开始

  const weeksAllHours = weeks.map((dayOfWeek) => {
    const closedReason = closedDateList.find(({ startDate, endDate }) => {
      return dayjs()
        .day(todayIndex + weeks.findIndex((i) => i === dayOfWeek))
        .isBetween(startDate, endDate, 'day', '[]');
    })?.description;
    if (closedReason) {
      return { dayOfWeek, closedReason };
    }
    const hours = workingHours[dayOfWeek.toLowerCase() as keyof StaffWorkingHoursEntity];
    if (!hours?.length) {
      return { dayOfWeek };
    }
    const times = hours.map(({ startTime, endTime }) => [getTime(startTime), getTime(endTime)]);
    return {
      dayOfWeek,
      hours: times.map(([start, end]) => `${start.format('h:mm A')} - ${end.format('h:mm A')}`),
      rawHours: times,
    };
  });

  const now = dayjs();
  const [today, ...restDay] = weeksAllHours;
  // eslint-disable-next-line prefer-const
  let { label = '', opening = false } = today.rawHours
    ? today.rawHours.reduce(
        (prev, [start, end], index) =>
          end.diff(now) >= 0 && start.diff(now) <= 0 ? { opening: true, label: `Today: ${today.hours[index]}` } : prev,
        { label: '', opening: false } as { label: string; opening: boolean },
      )
    : {};

  // 如果当前不可用，寻找今天的下一个可用时间，否则寻找今天之后的(直到下周的今天)下一个可用时间，如果所有时间均不可用，则label会是空字符串
  if (!opening) {
    const nextAvailableHourTodayIndex = today?.rawHours?.findIndex(([start]) => start.diff(now) >= 0) ?? -1;

    if (nextAvailableHourTodayIndex !== -1) {
      label = `Opens ${today.rawHours?.[nextAvailableHourTodayIndex][0].format('h:mm A')}`;
    } else {
      const nextAvailableHour = [...restDay, today].find((day) => day.rawHours);
      if (nextAvailableHour) {
        label = `Opens ${nextAvailableHour.rawHours?.[0][0].format('h:mm A')} ${nextAvailableHour.dayOfWeek}`;
      }
    }
  }

  return {
    opening,
    openStatusLabel: label,
    hours: weeksAllHours,
  };
});

function getTime(minutes: number): Dayjs {
  return dayjs()
    .hour(Math.floor(minutes / 60))
    .minute(minutes % 60);
}

export const businessLandingServicesState = loadable<
  | {
      serviceList: LandingServiceCategoryItem[];
      addonsList: LandingServiceCategoryItem[];
    }
  | undefined
>(
  atom(async () => {
    const [errors, result] = await promiseWrapper(
      Promise.all([
        http.open('GET/grooming/ob/v2/client/business/services', { type: 1 }, { stringifyError: false }),
        http.open('GET/grooming/ob/v2/client/business/services', { type: 2 }, { stringifyError: false }),
      ]),
    );
    if (errors) {
      // Unpublished
      if ((errors as any)?.find?.((error: any) => error?.data?.code === 40101)) {
        notFoundRoute.replace({ noBack: 1 });
        return;
      }
      const error = (errors as any)?.find?.((i: any) => !!i);
      throw error ? getErrorMessage(error) : 'Fetch services failed';
    }
    const [serviceList, addonsList] = result!;
    return { serviceList, addonsList };
  }),
  { throwError: true },
);

const limitMaxTipsAmountCountryList = ['australia', 'canada', 'united states', 'united kingdom'];
export const isLimitMaxTipsAmountCountry = function (country: string = '') {
  if (!country) {
    return false;
  }

  return limitMaxTipsAmountCountryList.includes(country.toLowerCase());
};

export const businessMaxTipsAmount = atom((get) => {
  const preference = get(businessPreferenceState).data;
  if (isLimitMaxTipsAmountCountry(preference?.country)) return MAX_TIPS_AMOUNT;
  return Infinity;
});

export const businessAvailabilityInfoState = atom((get) => {
  const onlineBookingSetting = (get(businessInfoState).data?.setting ?? {}) as BusinessInfoEntity['setting'];
  const {
    availableTimeType,
    timeslotFormat,
    bySlotTimeslotFormat,
    timeslotMins,
    bySlotTimeslotMins,
    arrivalWindowAfterMin,
    arrivalWindowBeforeMin,
    displayStaffSelectionPage,
    bookingRangeEndType,
    bookingRangeEndOffset,
    bookingRangeEndDate,
  } = onlineBookingSetting;

  return {
    availableTimeType,
    timeslotFormat: availableTimeType === AvailabilityType.ByWorkingHour ? timeslotFormat : bySlotTimeslotFormat,
    timeslotMins: availableTimeType === AvailabilityType.ByWorkingHour ? timeslotMins : bySlotTimeslotMins,
    arrivalWindowAfterMin,
    arrivalWindowBeforeMin,
    displayStaffSelectionPage,
    bookingRangeEndType,
    bookingRangeEndOffset,
    bookingRangeEndDate,
  };
});

export type BusinessServiceAreaPicCache = Pick<
  OpenApiModels['GET/grooming/service-area-pic-cache']['Res'],
  'url' | 'factorsHash'
>;
export const businessServiceAreaPicCacheState = loadable<BusinessServiceAreaPicCache>(
  atom(async () => {
    const { url, factorsHash } = (await http.open('GET/grooming/service-area-pic-cache')) || {};
    return { url, factorsHash };
  }),
);

export const businessGaGroupsState = atomWithReset<string>('');

export const businessAcceptCustomerTypeMapState = loadable(
  atom(async (get) => {
    /**
     * 注意：这里的接口返回的数据结构会包含所有的 serviceItemType，
     * 使用时，需要在业务中单独过滤出 biz 相关的 serviceItemType
     */
    const { acceptCustomerTypes } = await BookingAvailabilityServiceClient.getAcceptedCustomerTypes(
      get(bookingNamePayload),
    );
    const acceptCustomerTypeMap = acceptCustomerTypes.reduce((prev, serviceAcceptTypePair) => {
      prev[serviceAcceptTypePair.serviceItemType] = serviceAcceptTypePair.acceptCustomerType;
      return prev;
    }, {} as Record<ServiceItemType, AcceptCustomerType>);
    return acceptCustomerTypeMap;
  }),
);

// 类型守卫函数，用于检查响应是否为成功的脚本响应
const isValidScriptResponse = (
  res: GetOnlineBookingScriptSuccessResponse | GetOnlineBookingScriptErrorResponse,
): res is GetOnlineBookingScriptSuccessResponse => {
  return (
    res &&
    typeof res === 'object' &&
    'data' in res &&
    res.data &&
    typeof res.data === 'object' &&
    'css' in res.data &&
    'js' in res.data &&
    typeof res.data.css === 'string' &&
    typeof res.data.js === 'string'
  );
};

export const businessScriptsState = loadable(
  atom(async (get) => {
    const payload = get(bookingNamePayload);
    if (!payload.domain && !payload.name) {
      throw new Error('Missing required parameter "domain" or "name"');
    }
    const result = await BffPlatformClient.getOnlineBookingScriptForC(
      {
        name: payload.name,
        domain: payload.domain,
      },
      {
        skipErrorHandler: true,
      },
    );
    if (isValidScriptResponse(result)) {
      return result.data.js;
    }
    return '';
  }),
  { throwError: true },
);
