import { type OBClientInfoResponse } from '@moego/api-web/moego/api/online_booking/v1/ob_client_api';
import { type CustomerModelOnlineBookingView } from '@moego/api-web/moego/models/customer/v1/customer_models';
import { type EvaluationStatus } from '@moego/api-web/moego/models/customer/v1/customer_pet_enums';
import { type CustomerPetOnlineBookingView } from '@moego/api-web/moego/models/customer/v1/customer_pet_models';
import { type PetVaccineOnlineBookingView } from '@moego/api-web/moego/models/customer/v1/customer_pet_vaccine_models';
import { type PetUnavailableReason } from '@moego/api-web/moego/models/online_booking/v1/booking_availability_enums';
import { type AcceptCustomerType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { type DeepPartial } from '@moego/client-lib-http/dist/http-client';
import { type Overwrite } from 'utility-types';
import { type OpenApiDefinitions } from './openApi/schema';

export type BusinessInfoEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.InfoDto']['businessInfo'];

export type BusinessOBSettingEntity = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.mapperbean.MoeBusinessBookOnline']
>;

export type BusinessSquareInfoEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.InfoDto']['squareInfo'];

export type BusinessTwilioInfoEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.InfoDto']['twilioInfo'];

export type BusinessAvailablePetTypesEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.InfoDto']['availablePetTypes'];

export type BusinessProfileDTO = OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.ProfileDto'];

export type BusinessProfileEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.ProfileDto']['profile'];

export type BusinessPreferenceEntity =
  OpenApiDefinitions['grooming']['com.moego.server.business.dto.OBBusinessInfoDTO'];

export type BusinessOptionsEntity = DeepPartial<
  OpenApiDefinitions['business']['com.moego.server.business.service.dto.PreferenceOptionsDto']
>;

/**
 * entity with wrapper 的是新的 API 返回值
 * entity 是旧的 API 返回值，为了方便，把新的 flatten 成旧的再使用
 */
export type CustomerEntityWithWrapper = DeepPartial<OBClientInfoResponse>;
export type CustomerEntity = DeepPartial<CustomerModelOnlineBookingView> &
  Pick<
    DeepPartial<OBClientInfoResponse>,
    'customQuestions' | 'creditCardList' | 'requiredUpdate' | 'blockedServiceItemTypes'
  > & {
    birthday?: string;
  };

export type CustomerAddressEntity =
  OpenApiDefinitions['customer']['com.moego.server.customer.web.vo.ob.OBClientAddressVO'];

export type CustomerLastApptEntity = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.service.dto.ob.OBLastApptVO']
>;

export type CustomerApptListEntity = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.client.ClientApptListVO']
>;

export type CustomerApptItemEntity = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.client.ClientApptVO']
>;

export type CustomerApptDetailEntity = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO']
>;

export type BusinessLocationEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.ob.OBBusinessLocationVO'];

export type BusinessServicesEntity = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.ob.OBServiceListDto']
>;

export interface BookingQuestionEntity
  extends Overwrite<
    OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.GroomingQuestionDTO'],
    {
      createTime: number;
      updateTime: number;
      acceptCustomerType?: AcceptCustomerType;
    }
  > {}

export type BookingOptionEntity =
  OpenApiDefinitions['customer']['com.moego.server.customer.service.dto.ClientPetOptionsDto'];

export type PetTypeEntity = DeepPartial<OpenApiDefinitions['customer']['com.moego.server.customer.dto.MoePetTypeDTO']>;

export type PetBreedEntity = DeepPartial<
  OpenApiDefinitions['customer']['com.moego.server.customer.dto.MoePetBreedDTO']
>;

export type PetHairLengthEntity = DeepPartial<
  OpenApiDefinitions['customer']['com.moego.server.customer.dto.MoePetHairLengthDTO']
>;

export type PetFixedEntity = DeepPartial<
  OpenApiDefinitions['customer']['com.moego.server.customer.dto.MoePetFixedDTO']
>;

export type PetVaccineEntity = DeepPartial<
  OpenApiDefinitions['customer']['com.moego.server.customer.dto.PetVaccineListDTO']
>;

export type CustomerPetEntity = DeepPartial<
  OpenApiDefinitions['customer']['com.moego.server.grooming.params.BookOnlinePetParams']
>;

/**
 * entity with wrapper 的是新的 API 返回值
 * entity 是旧的 API 返回值，为了方便，把新的 flatten 成旧的再使用
 */
export type OBPetInfoResponse = {
  /** pet info */
  pet: CustomerPetOnlineBookingView & {
    /** evaluation status */
    evaluationStatus: EvaluationStatus;
  };
  /** ob custom question answers */
  petQuestionAnswers: { [key: string]: string };
  /** pet vaccine list */
  vaccineList: PetVaccineOnlineBookingView[];

  /** pet availability, unavailable reasons */
  unavailableReasons: PetUnavailableReason[];
};

export type CustomerExistPetWithWrapper = DeepPartial<OBPetInfoResponse>;

export type CustomerExistPetEntity = OBPetInfoResponse['pet'] &
  Pick<OBPetInfoResponse, 'petQuestionAnswers' | 'vaccineList' | 'unavailableReasons'>;

export type BookingAvailableTimeEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.service.dto.OBAvailableTimeDto'];

export type AvailableTimesEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.service.dto.OBAvailableDateTimeDTO'];

export type BookingAvailableTimesEntity = Record<
  string,
  OpenApiDefinitions['grooming']['com.moego.server.grooming.service.dto.OBAvailableTimeDto']
>;

export type BusinessGroomerEntity =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.ob.OBBusinessStaffVO'];

export type BusinessAgreementEntity = DeepPartial<
  OpenApiDefinitions['business']['com.moego.server.business.dto.BusinessAgreementDTO']
>;

export type OBClientInfo = OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.InfoDto'];

export type PrepayAmountDetail = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.PrepayAmountDTO']
>;

export type PreAuthAmountDetail = Partial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.PreAuthAmountDTO']
>;

export type OBSubmitInput = DeepPartial<
  DeepPartial<OpenApiDefinitions['grooming']['com.moego.server.grooming.web.params.OBClientInfoParams']>
>;

export type DepositStatus = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.BookOnlineDepositForClientDTO']
>;

export type OBPetDataDTO = DeepPartial<OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.ob.OBPetDataDTO']>;

export type LandingGalleryItem =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$OBLandingPageGalleryVO'];

export type LandingServiceCategoryItem =
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO'];

export type LandingPageServiceItem = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO$OBLandingPageServiceVO']
>;

export type StaffWorkingHoursEntity =
  OpenApiDefinitions['business']['com.moego.server.business.dto.StaffWorkingHourDayDetailDTO'];

export type AddressDetail = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.client.AddressVO']
>;

export type SubmitResult = DeepPartial<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.OBSubmitResultDTO']
>;

export type StaffWorkingHourRangeEntity = DeepPartial<
  OpenApiDefinitions['business']['com.moego.server.business.dto.TimeRangeDto']
>;

export type StaffWorkingHourRangesEntity = Record<
  string,
  OpenApiDefinitions['business']['com.moego.server.business.dto.TimeRangeDto']
>;
