import { Stripe } from 'stripe';
export namespace payment {
  export interface paths {
    'POST/payment/ach/createAndConfirm': operations['create'];
    'POST/payment/ach/payment/cancel': operations['cancel'];
    'POST/payment/ach/payment/status': operations['getPaymentStatus'];
    'POST/payment/ach/validate': operations['validateCode'];
    /** 添加新卡， 返回新增卡信息 */
    'POST/payment/card/add': operations['addCard_1'];
    /** 删除指定的卡， 返回被删除的卡信息 */
    'DELETE/payment/card/delete': operations['deleteCard'];
    /** 查询所有卡信息 */
    'GET/payment/card/list': operations['getCards'];
    'PUT/payment/card/setPrimary': operations['setPrimaryCard'];
    'POST/payment/cof/authenticate': operations['authentication'];
    'GET/payment/cof/client': operations['getClientInfo'];
    'POST/payment/cof/client/submit': operations['submitCof'];
    'PUT/payment/cof/code': operations['generateLink'];
    /** @deprecated */
    'GET/payment/creditcard/list': operations['selectByCustomerId'];
    /** 邮件包购买 */
    'POST/payment/email/buyEmailPackage': operations['buyEmailPackage'];
    /** 设置邮件包自动续费 */
    'POST/payment/email/setEmailAutoReload': operations['cancelAutoReload_1'];
    'GET/payment/getByPaymentId': operations['getPaymentById'];
    'POST/payment/hardware/order/buy': operations['buyHardwareOrder'];
    'POST/payment/hardware/order/preview': operations['previewHardwareOrder'];
    'GET/payment/hardware/shipping_methods': operations['listShippingMethods'];
    /** 给当前用户初始化session */
    'POST/payment/identity/session/get': operations['queryOrCreateSession'];
    /** 查询商家owner的identity状态 */
    'GET/payment/identity/status': operations['queryIdentityStatus'];
    'POST/payment/invoice/refund/submit': operations['submitRefund'];
    /** 短信包购买 */
    'POST/payment/msg/buyMsgPackage': operations['buyMsgPackage'];
    /** 查询短信包billing */
    'GET/payment/msg/getMsgBilling': operations['getMessageBilling'];
    /** 根据companyId查询可以购买的短信包， 不同地区加载不同的列表 */
    'GET/payment/msg/plan': operations['getMessagePlans'];
    /** 设置短信包自动续费 */
    'POST/payment/msg/setAutoReload': operations['cancelAutoReload'];
    'POST/payment/payment/bookOnline/addCard': operations['addCard'];
    'GET/payment/payment/bookOnline/intent': operations['createIntentForOB'];
    'POST/payment/payment/bookOnline/pay': operations['takePaymentForOB'];
    'GET/payment/payment/bookOnline/processingFee': operations['getObProcessingFee'];
    'GET/payment/payment/businessHasAccount': operations['businessHasVerifiedAccount'];
    'POST/payment/payment/connect/webhook': operations['processWebhookForApplication'];
    'POST/payment/payment/create': operations['createPayment'];
    'POST/payment/payment/createAndConfirm': operations['createAndConfirm'];
    'GET/payment/payment/intent': operations['createIntent'];
    'GET/payment/payment/list': operations['selectPageList'];
    'POST/payment/payment/list/export': operations['exportPageList'];
    'POST/payment/payment/payonline': operations['payonline'];
    'GET/payment/payment/processingFee': operations['getProcessingFee'];
    'PUT/payment/payment/signature': operations['saveSignature'];
    'GET/payment/payment/sum': operations['getAmountSumByCustomerId_1'];
    'POST/payment/payment/sum': operations['getAmountSumByCustomerId'];
    'POST/payment/payment/v2/bookOnline/pay': operations['onlineBookingPay'];
    'POST/payment/payment/webhook': operations['processWebhook_1'];
    /** 查询某个功能最低套餐的配置 */
    'GET/payment/plan/feature/need/config': operations['queryFeatureNeedPremiumType'];
    'GET/payment/platform-care/get/sign-record/code': operations['getSignRecordByCode_1'];
    'GET/payment/platform-care/record/code': operations['getPlatformCareRecordByCode'];
    'PUT/payment/platform-care/record/update': operations['updatePlatformCareRecord'];
    'GET/payment/platform-sales/get/sign-record/code': operations['getSignRecordByCode'];
    'GET/payment/platform-sales/record/agreement': operations['getPlatformSalesAgreementByCode'];
    'GET/payment/platform-sales/record/code': operations['getPlatformSalesRecordByCode'];
    'PUT/payment/platform-sales/record/status/update': operations['updatePlatformSalesRecordStatus'];
    'PUT/payment/platform-sales/record/update': operations['updatePlatformSalesRecord'];
    'POST/payment/preauth/capture': operations['capturePreAuth'];
    'POST/payment/preauth/card/switch': operations['switchStatus'];
    'GET/payment/preauth/detail': operations['checkStatus_1'];
    'POST/payment/preauth/pay': operations['payPreAuth'];
    'GET/payment/preauth/previous/status': operations['getPreviousPreAuthStatus'];
    'GET/payment/preauth/recent/card': operations['getRecentPMId'];
    'POST/payment/preauth/retry': operations['checkStatus'];
    'POST/payment/refund/check': operations['refundCheckByChangeAmount'];
    /** 退款请求的入口 */
    'POST/payment/refund/create': operations['createRefund'];
    'GET/payment/setting/info': operations['getPaymentSetting'];
    'PUT/payment/setting/info': operations['updatePaymentSetting'];
    'GET/payment/setting/processingFee/available': operations['isProcessingFeeAvailable'];
    'GET/payment/setting/processingFee/popup': operations['isNeedCloseProcessingFeeByClient'];
    'GET/payment/setting/ttp/info': operations['getTapToPayInfo'];
    'POST/payment/setting/ttp/info': operations['updateTapToPayInfo'];
    'GET/payment/smartTip/config': operations['getConfig'];
    'PUT/payment/smartTip/config': operations['updateConfig'];
    'GET/payment/split-payment/allconfig': operations['getAllConfig'];
    'POST/payment/split-payment/process': operations['processSplit'];
    /** 查询商家是否与Square Connected */
    'GET/payment/square/auth/token': operations['getToken'];
    /** 取得商家Square授权后，返回code，前端调用该接口生成accessToken */
    'POST/payment/square/auth/token': operations['saveTokenWithAuthCode'];
    'GET/payment/square/auth/url': operations['getAuthUrl'];
    'GET/payment/square/checkoutStatus': operations['getCheckoutPayment_1'];
    'POST/payment/square/client/payments': operations['takePaymentOnline'];
    /** 获取square customer and card info */
    'GET/payment/square/customers': operations['getCustomerInfo_1'];
    /** 创建Square Customer and card on file */
    'POST/payment/square/customers': operations['createCustomer_1'];
    'DELETE/payment/square/customers': operations['deleteCustomer'];
    'PUT/payment/square/customers/cof': operations['createCustomerCard'];
    'DELETE/payment/square/customers/cof': operations['deleteCustomerCard'];
    /** 断开与Square的连接 */
    'DELETE/payment/square/disconnect': operations['disconnectSquare'];
    /** 从square获取商家所有有效的locations， 有效的条件：状态为active， 且开通了CREDIT_CARD_PROCESSING */
    'GET/payment/square/locations': operations['queryActivePaymentLocations'];
    /** 设置商家take payment要使用的location */
    'PUT/payment/square/locations/default': operations['setDefaultLocation'];
    'POST/payment/square/payments': operations['takePayment'];
    'GET/payment/square/reader/authCode': operations['getReaderAuthCode'];
    'POST/payment/square/reader/payments': operations['recordMobileReaderPayment'];
    /** @deprecated */
    'PUT/payment/square/reader/payments/update': operations['updateReaderPaymentResult'];
    'PUT/payment/square/terminal/cancelPay': operations['cancelTerminalPayment'];
    'POST/payment/square/terminal/charge': operations['terminalCharge'];
    'GET/payment/square/terminal/checkoutStatus': operations['getCheckoutPayment'];
    /** create unpaired device code for terminal to connect */
    'POST/payment/square/terminal/createDeviceCode': operations['createDeviceCodeForTerminalPairing'];
    /** get unpaired device code for terminal to connect */
    'GET/payment/square/terminal/deviceCode': operations['getDeviceCodeForTerminalPairing'];
    /** get all paired devices code */
    'GET/payment/square/terminal/devices': operations['listTerminalDevices'];
    'POST/payment/square/webhook': operations['processWebhook'];
    'POST/payment/stripe/addBankAccount': operations['addBankAccount'];
    /** 针对绑定TestClock的stripe customer，可以修改对应的日期时间，从而触发billing cycle 更新 */
    'POST/payment/stripe/advanceClock': operations['advanceTestClock_1'];
    'POST/payment/stripe/attachPaymentMethod': operations['attachPaymentMethod'];
    /** @deprecated */
    'POST/payment/stripe/capturePaymentIntent': operations['capturePaymentIntent'];
    /** change status in stripe account meta data */
    'PUT/payment/stripe/changeAccount': operations['getAccount'];
    'POST/payment/stripe/checkCard': operations['checkCard'];
    'POST/payment/stripe/createAccountLinks': operations['createAccountLinks'];
    'POST/payment/stripe/createACHSetupIntent': operations['createACHSetupIntent'];
    'POST/payment/stripe/createCard': operations['createCard'];
    /** locationId为非必填， 不填写时创建全局token， 填写时，只能连接到指定locationId */
    'POST/payment/stripe/createConnToken': operations['createToken'];
    'POST/payment/stripe/createCustomer': operations['createCustomer'];
    'POST/payment/stripe/createLocation': operations['createLocation'];
    'POST/payment/stripe/createReader': operations['createReader'];
    'POST/payment/stripe/createStripeAccount': operations['createStripeAccount'];
    'POST/payment/stripe/customerStripe': operations['saveCustomerStripeInfo'];
    'POST/payment/stripe/deleteCardForCustomer': operations['deleteCardForCustomer'];
    'DELETE/payment/stripe/deleteExternalAccount': operations['deleteExternalAccount'];
    'DELETE/payment/stripe/deleteLocation': operations['deleteLocation'];
    'DELETE/payment/stripe/deleteReader': operations['deleteReader'];
    'GET/payment/stripe/dispute/readStatus': operations['getDisputeReadStatus'];
    'PUT/payment/stripe/dispute/readStatus': operations['updateDisputeReadStatus'];
    /** @deprecated */
    'GET/payment/stripe/disputes/list': operations['listDisputes'];
    'POST/payment/stripe/disputes/list': operations['listDispute'];
    'GET/payment/stripe/getAccount': operations['getAccount_1'];
    'GET/payment/stripe/getAllPersons': operations['getAllPersons'];
    'GET/payment/stripe/getBalance': operations['getBalance'];
    'GET/payment/stripe/getConnectedInfo': operations['getConnectedInfo'];
    'GET/payment/stripe/getCustomerInfo': operations['getCustomerInfo'];
    'GET/payment/stripe/getPaymentMethodList': operations['getPaymentMethodList'];
    /** @deprecated */
    'GET/payment/stripe/listDisputes': operations['listDispute_1'];
    'GET/payment/stripe/listPayouts': operations['listPayouts'];
    'GET/payment/stripe/locations': operations['getLocations'];
    'GET/payment/stripe/paymentMethod': operations['getPaymentMethod'];
    'DELETE/payment/stripe/paymentMethod': operations['deletePaymentMethod'];
    'GET/payment/stripe/payout/details': operations['getPayoutDetails'];
    'POST/payment/stripe/payout/details/export': operations['getPayoutDetailsExport'];
    'GET/payment/stripe/payout/details/summary': operations['getPayoutDetails_1'];
    'GET/payment/stripe/payout/list': operations['getPayoutList'];
    'POST/payment/stripe/payout/list/summary': operations['getPayoutListSummary'];
    /** 查询stripe payout schedule 状态 */
    'GET/payment/stripe/payout/schedule': operations['getPayoutSetting'];
    /** 更新stripe payout schedule delay delay days，默认为1天 */
    'PUT/payment/stripe/payout/schedule': operations['updatePayoutSetting'];
    'GET/payment/stripe/reader/action/info': operations['readerPaymentInfo'];
    'POST/payment/stripe/reader/cancel': operations['paymentCancel'];
    'POST/payment/stripe/reader/process': operations['paymentProcess'];
    'GET/payment/stripe/reader/status': operations['checkPaymentStatus'];
    /** 指定location下所有关联的reader */
    'GET/payment/stripe/readers': operations['getReaders'];
    /** @deprecated */
    'POST/payment/stripe/setDefaultExternalAccount': operations['setDefaultExternalAccount'];
    /** 获取stripe customer的Test Clock时间 */
    'GET/payment/stripe/testClock': operations['getTestClock_1'];
    'POST/payment/stripe/updateLocation': operations['updateLocation'];
    /** 激活订阅 */
    'POST/payment/subscription/activateSubscription': operations['activateSubscription'];
    /** 针对绑定TestClock的stripe customer，可以修改对应的日期时间，从而触发billing cycle 更新 */
    'POST/payment/subscription/advanceClock': operations['advanceTestClock'];
    /**
     * 购买套餐
     * @deprecated
     */
    'POST/payment/subscription/buyPlanByCard': operations['buyPlanByCard'];
    /** 取消订阅 */
    'POST/payment/subscription/cancelSubscription': operations['cancelSubscription'];
    'POST/payment/subscription/changeEnterprisePlan': operations['changeEnterprisePlan'];
    /** 切换套餐: upgrade/downgrade  */
    'POST/payment/subscription/changePlan': operations['changePlan'];
    'GET/payment/subscription/charges': operations['getCharges'];
    'GET/payment/subscription/coupon': operations['getCoupon'];
    'GET/payment/subscription/coupon-v2': operations['getCoupon_1'];
    /** 购买套餐新接口 */
    'POST/payment/subscription/createNew': operations['createNew'];
    'POST/payment/subscription/downgradeEnterprisePlanImmediately': operations['downgradeEnterprisePlanImmediately'];
    'GET/payment/subscription/enterprise/config': operations['getEnterpriseSubscriptionConfig'];
    'POST/payment/subscription/enterprise/config/update': operations['updateEnterpriseSubscriptionConfig'];
    'POST/payment/subscription/enterprise/subscription/fixPackageMsg': operations['fixEnterpriseSubscriptionPackageMsg'];
    'POST/payment/subscription/enterprise/subscription/listFranchiseeBillDatesAndCards': operations['listFranchiseeBillDatesAndCards'];
    'GET/payment/subscription/enterprise/subscription/retrieve': operations['retrieveEnterpriseSubscription'];
    'GET/payment/subscription/enterpriseCharges': operations['getEnterpriseCharges'];
    /** 查询商家订阅信息 */
    'GET/payment/subscription/getBillingData': operations['getBillingData'];
    /** 查询该account下所有公司信息，若该account是公司owner，下发billing信息 */
    'GET/payment/subscription/getCompaniesAndBillingData': operations['getCompaniesAndBilling'];
    /** @deprecated */
    'GET/payment/subscription/getCompanyCardInfo': operations['getCompanyCardInfo'];
    /** 查询该account下所有 enterprise 信息，若该account是 enterprise owner，下发billing信息 */
    'GET/payment/subscription/getEnterprisesAndBillingData': operations['getEnterprisesAndBillingData'];
    /** 获取所有套餐变更历史 */
    'GET/payment/subscription/history': operations['getOrderList'];
    /** 主动触发订阅对象subscription下的invoice支付, 可指定支付的cardId */
    'POST/payment/subscription/pay': operations['reCharge'];
    'GET/payment/subscription/planInfo': operations['getPlanInfo'];
    /** 获取所有套餐列表 */
    'GET/payment/subscription/plans': operations['getAllPlans'];
    /** 修改绑定卡信息 */
    'POST/payment/subscription/replacePaymentCard': operations['replacePaymentCard'];
    'POST/payment/subscription/revertDowngrade': operations['revertDowngrade'];
    'GET/payment/subscription/subscription/retrieve': operations['retrieveCompanySubscription'];
    /**
     * 切换套餐
     * @deprecated
     */
    'POST/payment/subscription/switchSubscription': operations['switchSubscription'];
    /** @deprecated */
    'POST/payment/subscription/sync/grandplan': operations['syncAll'];
    /** 获取company下对应stripe customer的Test Clock时间 */
    'GET/payment/subscription/testClock': operations['getTestClock'];
    'POST/payment/test': operations['test'];
  }
  export interface operations {
    create: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.CreateACHPaymentVo']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.ACHMandateConfirmResp'];
    };
    cancel: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.ACHQueryVo']>;
      Res: {};
    };
    getPaymentStatus: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.ACHQueryVo']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.ACHDetailResp'];
    };
    validateCode: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.ACHValidateCodeVo']>;
      Res: {};
    };
    addCard_1: {
      Req: Partial<
        ({
          companyId?: number;
        } & {
          enterpriseId?: string | number;
        }) &
          components['schemas']['com.moego.server.payment.params.CardParams']
      >;
      Res: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'];
    };
    deleteCard: {
      Req: Partial<
        {
          cardId: string;
        } & {
          companyId?: number;
        } & {
          enterpriseId?: string | number;
        }
      >;
      Res: {};
    };
    getCards: {
      Req: Partial<
        {
          companyId?: number;
        } & {
          enterpriseId?: string | number;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'][];
    };
    setPrimaryCard: {
      Req: Partial<
        {
          cardId: string;
        } & {
          companyId?: number;
        } & {
          enterpriseId?: string | number;
        }
      >;
      Res: {};
    };
    authentication: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.CardAuthenticationReq']>;
      Res: number;
    };
    getClientInfo: {
      Req: Partial<{
        c: string;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.CardClientDto'];
    };
    submitCof: {
      Req: Partial<
        {
          c: string;
        } & components['schemas']['com.moego.server.payment.params.CardRequestParams']
      >;
      Res: string;
    };
    generateLink: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.CardRequestDTO'];
    };
    selectByCustomerId: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResult'];
    };
    buyEmailPackage: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.EmailBuyParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    cancelAutoReload_1: {
      Req: Partial<
        {
          autoReload?: number;
        } & {
          companyId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getPaymentById: {
      Req: Partial<{
        paymentId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentDTO'];
    };
    buyHardwareOrder: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.stripe.HardwareOrderParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.HardwareOrderDTO'];
    };
    previewHardwareOrder: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.stripe.HardwareOrderParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.HardwareOrderDTO'];
    };
    listShippingMethods: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.ShippingMethodCollection'];
    };
    queryOrCreateSession: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.CreateIdentitySessionRequest']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.IdentityStatusResponse'];
    };
    queryIdentityStatus: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.web.dto.IdentityStatusResponse'];
    };
    submitRefund: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.SubmitInvoiceRefundVo']>;
      Res: {};
    };
    buyMsgPackage: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.MsgBuyParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getMessageBilling: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.MessageBillingDTO'];
    };
    getMessagePlans: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.MessagePriceDTO'];
    };
    cancelAutoReload: {
      Req: Partial<
        {
          autoReload?: number;
        } & {
          companyId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    addCard: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.payment.params.CardParams']
      >;
      Res: components['schemas']['com.moego.server.payment.dto.CustomerStripInfoSaveResponse'];
    };
    createIntentForOB: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessName: string;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.dto.PaymentIntentResponse'];
    };
    takePaymentForOB: {
      Req: Partial<
        (components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          guid: string;
        }) &
          components['schemas']['com.moego.server.payment.params.BookOnlinePayParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PaymentSummary$PaymentDto'];
    };
    getObProcessingFee: {
      Req: Partial<
        {
          amount: number;
        } & components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
            stripePaymentMethod: number;
          }
      >;
      Res: components['schemas']['com.moego.server.payment.dto.PaymentProcessingFeeDTO'];
    };
    businessHasVerifiedAccount: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.BusinessHasValidAccountResponse'];
    };
    processWebhookForApplication: {
      Req: Partial<string>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    createPayment: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.CreatePaymentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PaymentSummary$PaymentDto'];
    };
    createAndConfirm: {
      Req: Partial<
        {
          guid?: string;
        } & components['schemas']['com.moego.server.payment.params.CreatePaymentParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PaymentSummary$PaymentDto'];
    };
    createIntent: {
      Req: Partial<{
        guid: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentIntentResponse'];
    };
    selectPageList: {
      Req: Partial<
        {
          customerId?: number;
        } & {
          endDate?: string;
        } & {
          invoiceId?: number;
        } & {
          method?: string;
        } & {
          module?: string;
        } & {
          order?: string;
        } & {
          pageNum?: number;
        } & {
          pageSize?: number;
        } & {
          searchBusinessId?: number;
        } & {
          startDate?: string;
        } & {
          transactionId?: string | number;
        } & {
          /** @description 指定method为Credit card时，该参数生效。vendor 取值： 'stripe'（default）：查询stripe credit card； 'square':查询square； 'all':查询所有的 */
          vendor?: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentListDto'];
    };
    exportPageList: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.TransactionHistoryReq']>;
      Res: string;
    };
    payonline: {
      Req: Partial<
        {
          guid: string;
        } & components['schemas']['com.moego.server.payment.params.CreatePaymentParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PaymentSummary$PaymentDto'];
    };
    getProcessingFee: {
      Req: Partial<
        {
          amount: number;
        } & {
          stripePaymentMethod: number;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.dto.PaymentProcessingFeeDTO'];
    };
    saveSignature: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.PaymentSigRequest']>;
      Res: number;
    };
    getAmountSumByCustomerId_1: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentAmountSumDto'];
    };
    getAmountSumByCustomerId: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.GetByCustomerIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentAmountSumDto'];
    };
    onlineBookingPay: {
      Req: Partial<
        (components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          guid: string;
        }) &
          components['schemas']['com.moego.server.payment.web.param.OnlineBookingPayParams']
      >;
      Res: components['schemas']['com.moego.common.dto.PaymentSummary$PaymentDto'];
    };
    processWebhook_1: {
      Req: Partial<string>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    queryFeatureNeedPremiumType: {
      Req: Partial<{}>;
      Res: {
        [key: string]: components['schemas']['com.moego.server.payment.web.dto.FeatureRequireConfigDto'] | undefined;
      };
    };
    getSignRecordByCode_1: {
      Req: Partial<{
        code: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.SignAgreementRecordByCodeResp'];
    };
    getPlatformCareRecordByCode: {
      Req: Partial<{
        code: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.PlatformCareRecordByCodeResp'];
    };
    updatePlatformCareRecord: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.dto.UpdatePlatformCareRecordDTO']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Long'];
    };
    getSignRecordByCode: {
      Req: Partial<{
        code: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.SignAgreementRecordByCodeResp'];
    };
    getPlatformSalesAgreementByCode: {
      Req: Partial<{
        code: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.PlatformSalesAgreementByCodeResp'];
    };
    getPlatformSalesRecordByCode: {
      Req: Partial<{
        code: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.PlatformSalesRecordByCodeResp'];
    };
    updatePlatformSalesRecordStatus: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.dto.UpdatePlatformSalesStatusDTO']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    updatePlatformSalesRecord: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.dto.UpdatePlatformSalesRecordDTO']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Long'];
    };
    capturePreAuth: {
      Req: Partial<{
        ticketId: number;
      }>;
      Res: {};
    };
    switchStatus: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.PreAuthSwitchVo']>;
      Res: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
    };
    checkStatus_1: {
      Req: Partial<{
        ticketId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
    };
    payPreAuth: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.PreAuthPayVo']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.PreAuthPayResp'];
    };
    getPreviousPreAuthStatus: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: boolean;
    };
    getRecentPMId: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.PreAuthCardDto'];
    };
    checkStatus: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.PreAuthRetryParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
    };
    refundCheckByChangeAmount: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.CheckRefundChannelParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.RefundChannelDTO'];
    };
    createRefund: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.CreateRefundParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentDTO'];
    };
    getPaymentSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.dto.PaymentSettingDTO'];
    };
    updatePaymentSetting: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.PaymentSettingDTO']>;
      Res: components['schemas']['com.moego.server.payment.dto.PaymentSettingDTO'];
    };
    isProcessingFeeAvailable: {
      Req: Partial<{}>;
      Res: boolean;
    };
    isNeedCloseProcessingFeeByClient: {
      Req: Partial<{
        addressState: string;
      }>;
      Res: boolean;
    };
    getTapToPayInfo: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.TapToPaySettingDTO'];
    };
    updateTapToPayInfo: {
      Req: Partial<components['schemas']['com.moego.server.payment.TapToPaySettingDTO']>;
      Res: components['schemas']['com.moego.server.payment.TapToPaySettingDTO'];
    };
    getConfig: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.dto.SmartTipConfigDTO'];
    };
    updateConfig: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.SmartTipConfigParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.SmartTipConfigDTO'];
    };
    getAllConfig: {
      Req: Partial<{}>;
      Res: {
        [key: string]: any;
      };
    };
    processSplit: {
      Req: Partial<{}>;
      Res: string;
    };
    getToken: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.GetSquareTokenResponse'];
    };
    saveTokenWithAuthCode: {
      Req: Partial<
        {
          /** @description 商家授权后，square回调带回的query string参数 code， ex: sandbox-sq0cgb-ExBnXXZZW2JXeBe897yyLA(测试环境) */
          authCode: string;
        } & {
          /** @description 商家授权后，square回调带回的query string参数 state，ex: 100000-q2vkzs69tr */
          state: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareTokenUpdateResponse'];
    };
    getAuthUrl: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.GetSquareAuthUrlResponse'];
    };
    getCheckoutPayment_1: {
      Req: Partial<{
        primaryId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.PaymentDTO'];
    };
    takePaymentOnline: {
      Req: Partial<
        {
          guid: string;
        } & components['schemas']['com.moego.server.payment.dto.square.SquarePaymentRequest']
      >;
      Res: components['schemas']['com.moego.server.payment.dto.square.SquareTakePaymentResponse'];
    };
    getCustomerInfo_1: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareCustomerInfo'];
    };
    createCustomer_1: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquareCreateCustomerRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareCreateCustomerResponse'];
    };
    deleteCustomer: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    createCustomerCard: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquareCreateCustomerRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareCreateCOFResponse'];
    };
    deleteCustomerCard: {
      Req: Partial<
        {
          /** @description 要删除的square card id， ex: ccof:CvV522cF1NdNUjll3GB */
          cardId: string;
        } & {
          customerId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    disconnectSquare: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    queryActivePaymentLocations: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.square.SquareLocation'];
    };
    setDefaultLocation: {
      Req: Partial<{
        locationId: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SetSquareDefaultLocationResponse'];
    };
    takePayment: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquarePaymentRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareTakePaymentResponse'];
    };
    getReaderAuthCode: {
      Req: Partial<{
        locationId?: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.ReaderAuthCodeResponse'];
    };
    recordMobileReaderPayment: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquarePaymentRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareTakePaymentResponse'];
    };
    updateReaderPaymentResult: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquareReaderPaymentSyncParam']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    cancelTerminalPayment: {
      Req: Partial<{
        checkoutId: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.TerminalChargeResponse'];
    };
    terminalCharge: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquarePaymentRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.TerminalChargeResponse'];
    };
    getCheckoutPayment: {
      Req: Partial<{
        checkoutId: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.PaymentDTO'];
    };
    createDeviceCodeForTerminalPairing: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.square.SquareDevice']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareDeviceCodeResponse'];
    };
    getDeviceCodeForTerminalPairing: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.square.SquareDeviceCodeResponse'];
    };
    listTerminalDevices: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.square.SquareDevice'];
    };
    processWebhook: {
      Req: Partial<string>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    addBankAccount: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.AddBankAccountRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.Account'];
    };
    advanceTestClock_1: {
      Req: Partial<components['schemas']['com.moego.server.payment.service.params.ClientTestClockParam']>;
      Res: {};
    };
    attachPaymentMethod: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.AttachPaymentMethodRequest']>;
      Res: components['schemas']['com.moego.server.payment.dto.CardDTO'];
    };
    capturePaymentIntent: {
      Req: Partial<{
        paymentIntentId: string;
      }>;
      Res: boolean;
    };
    getAccount: {
      Req: Partial<{
        status: string;
      }>;
      Res: {};
    };
    checkCard: {
      Req: Partial<
        {
          businessName: string;
        } & {
          chargeToken: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.AddResultDTO'];
    };
    createAccountLinks: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.CreateAccountLinksRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.AccountLink'];
    };
    createACHSetupIntent: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.CreateACHSetupIntentRequest']>;
      Res: components['schemas']['com.moego.server.payment.dto.SetupIntentDTO'];
    };
    createCard: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.CreateCardRequest']>;
      Res: components['schemas']['com.moego.server.payment.dto.CardDTO'];
    };
    createToken: {
      Req: Partial<{
        locationId?: string;
      }>;
      Res: string;
    };
    createCustomer: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.CreateCustomerRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.Customer'];
    };
    createLocation: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.stripe.LocationParam']>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.LocationDto'];
    };
    createReader: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.stripe.ReaderParam']>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.ReaderDto'];
    };
    createStripeAccount: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.CreateStripeAccountRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.Account'];
    };
    saveCustomerStripeInfo: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.CustomerStripInfoRequest']>;
      Res: components['schemas']['com.moego.server.payment.dto.CustomerStripInfoSaveResponse'];
    };
    deleteCardForCustomer: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.DeleteCardForCustomerRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    deleteExternalAccount: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.DeleteExternalAccountRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.ExternalAccount'];
    };
    deleteLocation: {
      Req: Partial<{
        locationId: string;
      }>;
      Res: boolean;
    };
    deleteReader: {
      Req: Partial<{
        stripeReaderId: string;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.ReaderDto'];
    };
    getDisputeReadStatus: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.web.vo.DisputeReadStatusVO'];
    };
    updateDisputeReadStatus: {
      Req: Partial<{}>;
      Res: {};
    };
    listDisputes: {
      Req: Partial<
        {
          pageNo?: number;
        } & {
          pageSize?: number;
        }
      >;
      Res: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.payment.web.vo.StripeDisputeVo'];
    };
    listDispute: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.DisputeQueryVO']>;
      Res: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.payment.web.vo.StripeDisputeVo'];
    };
    getAccount_1: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.Account'];
    };
    getAllPersons: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.PersonCollection'];
    };
    getBalance: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.Balance'];
    };
    getConnectedInfo: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.web.dto.StripeConnectedInfo'];
    };
    getCustomerInfo: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.Customer'];
    };
    getPaymentMethodList: {
      Req: Partial<
        {
          customerId: number;
        } & {
          methodType?: number;
        }
      >;
      Res: Stripe.ApiList<Stripe.PaymentMethod>;
    };
    listDispute_1: {
      Req: Partial<
        {
          limit: number;
        } & {
          startingAfter?: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.DisputeCollection'];
    };
    listPayouts: {
      Req: Partial<
        {
          limit: number;
        } & {
          startingAfter?: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.PayoutCollection'];
    };
    getLocations: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.LocationDto'][];
    };
    getPaymentMethod: {
      Req: Partial<{
        paymentIntentId: string;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.PaymentMethodDto'];
    };
    deletePaymentMethod: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.stripe.PaymentMethodParam']>;
      Res: boolean;
    };
    getPayoutDetails: {
      Req: Partial<
        {
          offset?: string;
        } & {
          pageSize?: number;
        } & {
          payoutId: string;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.web.dto.PayoutDetails'];
    };
    getPayoutDetailsExport: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.PayoutDetailsExportReq']>;
      Res: string;
    };
    getPayoutDetails_1: {
      Req: Partial<{
        payoutId: string;
      }>;
      Res: components['schemas']['com.moego.server.payment.web.dto.PayoutDetails'];
    };
    getPayoutList: {
      Req: Partial<
        {
          endCreatedDate?: string;
        } & {
          offset?: string;
        } & {
          pageSize?: number;
        } & {
          startCreatedDate?: string;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.web.dto.PayoutViewList'];
    };
    getPayoutListSummary: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.PayoutListSummaryVo']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.PayoutListSummaryResp'];
    };
    getPayoutSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.web.dto.PayoutResp'];
    };
    updatePayoutSetting: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.PayoutReq']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.PayoutResp'];
    };
    readerPaymentInfo: {
      Req: Partial<{
        readerId: string;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.ReaderActionInfoResp'];
    };
    paymentCancel: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.ReaderCancelReq']>;
      Res: boolean;
    };
    paymentProcess: {
      Req: Partial<components['schemas']['com.moego.server.payment.web.vo.ReaderProcessReq']>;
      Res: boolean;
    };
    checkPaymentStatus: {
      Req: Partial<
        {
          payIntentId: string;
        } & {
          readerId: string;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.dto.ReaderStatusDTO'];
    };
    getReaders: {
      Req: Partial<
        {
          deviceType?: string;
        } & {
          locationId: string;
        }
      >;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.ReaderDto'][];
    };
    setDefaultExternalAccount: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.SetDefaultExternalAccountRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.stripe.model.ExternalAccount'];
    };
    getTestClock_1: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.web.vo.TestClockVO'];
    };
    updateLocation: {
      Req: Partial<components['schemas']['com.moego.server.payment.dto.stripe.LocationParam']>;
      Res: components['schemas']['com.moego.server.payment.dto.stripe.LocationDto'];
    };
    activateSubscription: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    advanceTestClock: {
      Req: Partial<components['schemas']['com.moego.server.payment.service.params.StripeTestClockParam']>;
      Res: {};
    };
    buyPlanByCard: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.SubscriptionParams']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.SubscriptionDTO'];
    };
    cancelSubscription: {
      Req: Partial<
        {
          companyId: number;
        } & components['schemas']['com.moego.server.payment.params.CancelSubParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    changeEnterprisePlan: {
      Req: Partial<components['schemas']['com.moego.server.payment.service.params.EnterpriseSubscriptionUpdateParam']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.SubscriptionDTO'];
    };
    changePlan: {
      Req: Partial<components['schemas']['com.moego.server.payment.service.params.SubscriptionUpdateParam']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.SubscriptionDTO'];
    };
    getCharges: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.ChargeDTO'];
    };
    getCoupon: {
      Req: Partial<{
        couponCode: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.CouponDTO'];
    };
    getCoupon_1: {
      Req: Partial<
        {
          companyId: number;
        } & {
          couponCode: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.CouponDTO'];
    };
    createNew: {
      Req: Partial<components['schemas']['com.moego.server.payment.service.params.SubscriptionCreateParam']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.SubscriptionDTO'];
    };
    downgradeEnterprisePlanImmediately: {
      Req: Partial<components['schemas']['com.moego.server.payment.service.params.EnterpriseSubscriptionUpdateParam']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.SubscriptionDTO'];
    };
    getEnterpriseSubscriptionConfig: {
      Req: Partial<{
        enterpriseId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.payment.dto.EnterpriseSubscriptionConfigDTO'];
    };
    updateEnterpriseSubscriptionConfig: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.UpdateEnterpriseSubscriptionConfigParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.EnterpriseSubscriptionConfigDTO'];
    };
    fixEnterpriseSubscriptionPackageMsg: {
      Req: Partial<(string | number)[]>;
      Res: {};
    };
    listFranchiseeBillDatesAndCards: {
      Req: Partial<{
        enterpriseId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.payment.web.dto.ListFranchiseeBillDatesAndCardsResp'];
    };
    retrieveEnterpriseSubscription: {
      Req: Partial<{
        enterpriseId: string | number;
      }>;
      Res: {
        /** @enum {string} */
        __stripe__: PathsPaymentSubscriptionEnterpriseSubscriptionRetrieveGetResponses200Content__stripe__;
      };
    };
    getEnterpriseCharges: {
      Req: Partial<{
        enterpriseId: string | number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.ChargeDTO'];
    };
    getBillingData: {
      Req: Partial<{
        /** @description query multiple companies by companyIds, ex: companyIds=1,2,3 */
        companyIds: number[];
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.BillingDataDTO'];
    };
    getCompaniesAndBilling: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.CompanyAndBillingDTO'];
    };
    getCompanyCardInfo: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.CompanyCardDTO'];
    };
    getEnterprisesAndBillingData: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.payment.dto.EnterpriseBillingDTO'][];
    };
    getOrderList: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.web.vo.SubscriptionOrderVo'][];
    };
    reCharge: {
      Req: Partial<
        {
          cardId?: string;
        } & {
          companyId: number;
        }
      >;
      Res: string;
    };
    getPlanInfo: {
      Req: Partial<{}>;
      Res: {
        [key: string]: components['schemas']['com.moego.common.utils.payment.PlanInfo'] | undefined;
      };
    };
    getAllPlans: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.SubscriptionPlanDTO'];
    };
    replacePaymentCard: {
      Req: Partial<
        {
          companyId: number;
        } & components['schemas']['com.moego.server.payment.params.CardParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.CompanyCardDTO'];
    };
    revertDowngrade: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: {};
    };
    retrieveCompanySubscription: {
      Req: Partial<{
        companyId: string | number;
      }>;
      Res: {
        /** @enum {string} */
        __stripe__: PathsPaymentSubscriptionSubscriptionRetrieveGetResponses200Content__stripe__;
      };
    };
    switchSubscription: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.SubscriptionParams']>;
      Res: components['schemas']['com.moego.server.payment.web.dto.SubscriptionDTO'];
    };
    syncAll: {
      Req: Partial<{}>;
      Res: string;
    };
    getTestClock: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.server.payment.web.vo.TestClockVO'];
    };
    test: {
      Req: Partial<components['schemas']['com.moego.server.payment.params.TestParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.payment.dto.TestDTO'];
    };
  }
  export interface components {
    schemas: {
      'com.moego.common.dto.AddResultDTO': {
        /** Format: int32 */
        id: number;
        result: boolean;
      };
      /** @description tier level */
      'com.moego.common.dto.NewPricingLevelDto': {
        /**
         * Format: int32
         * @description plan version
         */
        planVersion: number;
        /**
         * Format: int32
         * @description 付费级别 0 1 2 3
         */
        premiumType: number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.payment.web.vo.StripeDisputeVo': {
        dataList: components['schemas']['com.moego.server.payment.web.vo.StripeDisputeVo'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PaymentSummary$PaymentDto': {
        amount: number;
        cardFunding: string;
        cardNumber: string;
        cardType: string;
        checkNumber: string;
        /** Format: int64 */
        createTime: string | number;
        description: string;
        expMonth: string;
        expYear: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isDeposit: number;
        isOnline: boolean;
        isPrepay: boolean;
        merchant: string;
        method: string;
        /** Format: int64 */
        orderPaymentId: string | number;
        paidBy: string;
        processingFee: number;
        signature: string;
        /** Format: int32 */
        squarePaymentMethod: number;
        status: string;
        stripeAccountId: string;
        stripeClientSecret: string;
        stripeCustomerId: string;
        stripeIntentId: string;
        /** Format: int32 */
        stripePaymentMethod: number;
        stripeStatus: string;
        /** Format: int64 */
        transactionId: string | number;
        /** Format: int64 */
        updateTime: string | number;
        /** @description values: Stripe, Square or empty string */
        vendor: string;
      };
      'com.moego.common.response.ResponseResult': {
        /** Format: int32 */
        code: number;
        data: any;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.AddResultDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.AddResultDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PaymentSummary$PaymentDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PaymentSummary$PaymentDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.CompanyCardDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.CouponDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.CouponDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.GetSquareTokenResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.GetSquareTokenResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.MessageBillingDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.MessageBillingDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentAmountSumDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.PaymentAmountSumDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.PaymentDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentIntentResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.PaymentIntentResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.PaymentListDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.PaymentListDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.ReaderAuthCodeResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.ReaderAuthCodeResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.GetSquareAuthUrlResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.GetSquareAuthUrlResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SetSquareDefaultLocationResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SetSquareDefaultLocationResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareCreateCOFResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareCreateCOFResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareCreateCustomerResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareCreateCustomerResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareCustomerInfo': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareCustomerInfo'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareDeviceCodeResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareDeviceCodeResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareTakePaymentResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareTakePaymentResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.SquareTokenUpdateResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareTokenUpdateResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.square.TerminalChargeResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.TerminalChargeResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.dto.TestDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.TestDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.BusinessHasValidAccountResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.web.dto.BusinessHasValidAccountResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.PlatformCareRecordByCodeResp': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.web.dto.PlatformCareRecordByCodeResp'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.PlatformSalesAgreementByCodeResp': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.web.dto.PlatformSalesAgreementByCodeResp'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.PlatformSalesRecordByCodeResp': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.web.dto.PlatformSalesRecordByCodeResp'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.payment.web.dto.SignAgreementRecordByCodeResp': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.web.dto.SignAgreementRecordByCodeResp'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.Account': {
        /** Format: int32 */
        code: number;
        data: Stripe.Account;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.AccountLink': {
        /** Format: int32 */
        code: number;
        data: Stripe.AccountLink;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.Balance': {
        /** Format: int32 */
        code: number;
        data: Stripe.Balance;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.Customer': {
        /** Format: int32 */
        code: number;
        data: Stripe.Customer;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.DisputeCollection': {
        /** Format: int32 */
        code: number;
        data: Stripe.ApiList<Stripe.Dispute>;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.ExternalAccount': {
        /** Format: int32 */
        code: number;
        data: Stripe.ApiList<Stripe.BankAccount>;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.PayoutCollection': {
        /** Format: int32 */
        code: number;
        data: Stripe.ApiList<Stripe.Payout>;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.stripe.model.PersonCollection': {
        /** Format: int32 */
        code: number;
        data: Stripe.ApiList<Stripe.Person>;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.Integer': {
        /** Format: int32 */
        code: number;
        /** Format: int32 */
        data: number;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.Long': {
        /** Format: int32 */
        code: number;
        /** Format: int64 */
        data: string | number;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.String': {
        /** Format: int32 */
        code: number;
        data: string;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.BillingDataDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.BillingDataDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.ChargeDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.ChargeDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.CompanyAndBillingDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.CompanyAndBillingDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.MessagePriceDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.MessagePriceDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.PaymentDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.PaymentDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.square.SquareDevice': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareDevice'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.square.SquareDeviceCodeResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareDeviceCodeResponse'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.square.SquareLocation': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.square.SquareLocation'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.payment.dto.SubscriptionPlanDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.payment.dto.SubscriptionPlanDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.utils.payment.MsgPair': {
        /** Format: int32 */
        numUkAu: number;
        /** Format: int32 */
        numUsCa: number;
      };
      'com.moego.common.utils.payment.PlanInfo': {
        fee: components['schemas']['com.moego.common.utils.payment.ProcessingFee'];
        msgSal: components['schemas']['com.moego.common.utils.payment.MsgPair'];
        msgVan: components['schemas']['com.moego.common.utils.payment.MsgPair'];
        /** Format: int32 */
        staffLimit: number;
      };
      'com.moego.common.utils.payment.ProcessingFee': {
        online: components['schemas']['com.moego.common.utils.payment.ProcessingFeePair'];
        terminal: components['schemas']['com.moego.common.utils.payment.ProcessingFeePair'];
      };
      'com.moego.common.utils.payment.ProcessingFeePair': {
        /**
         * Format: int32
         * @description 单位美分
         */
        cent: number;
        /**
         * Format: double
         * @description 百分比，收费2.9%, 记为0.029
         */
        percent: number;
      };
      'com.moego.server.business.dto.EnterpriseDto': {
        /** @description owner account id */
        accountId: string;
        /** @description enterprise id */
        id: string;
        name: string;
      };
      'com.moego.server.business.dto.MoeBusinessDto': {
        address: string;
        address1: string;
        address2: string;
        addressCity: string;
        addressCountry: string;
        addressLat: string;
        addressLng: string;
        addressState: string;
        addressZipcode: string;
        /** Format: int32 */
        appType: number;
        avatarPath: string;
        /** @deprecated */
        bookOnlineName: string;
        businessName: string;
        calendarFormat: string;
        /** Format: int32 */
        calendarFormatType: number;
        /** Format: int32 */
        clockInOutEnable: number;
        /** Format: int32 */
        clockInOutNotify: number;
        /** Format: int32 */
        companyId: number;
        contactEmail: string;
        country: string;
        countryAlpha2Code: string;
        /** @deprecated */
        countryCode: string;
        /** Format: int64 */
        createTime: string | number;
        currencyCode: string;
        currencySymbol: string;
        dateFormat: string;
        /** Format: int32 */
        dateFormatType: number;
        facebook: string;
        google: string;
        /** Format: int32 */
        id: number;
        instagram: string;
        /** Format: int32 */
        isEnableAccessCode: number;
        knowFrom: string;
        /** Format: int32 */
        locations: number;
        /** Format: int32 */
        moveFrom: number;
        needSendCode: boolean;
        numberFormat: string;
        /** Format: int32 */
        numberFormatType: number;
        ownerEmail: string;
        phoneNumber: string;
        /** Format: int32 */
        primaryPayType: number;
        /** Format: int32 */
        retailEnable: number;
        /** Format: int32 */
        serviceAreaEnable: number;
        smartScheduleEndAddr: string;
        smartScheduleEndLat: string;
        smartScheduleEndLng: string;
        /** Format: int32 */
        smartScheduleMaxDist: number;
        /** Format: int32 */
        smartScheduleMaxTime: number;
        /** Format: int32 */
        smartScheduleServiceRange: number;
        smartScheduleStartAddr: string;
        smartScheduleStartLat: string;
        smartScheduleStartLng: string;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffMembers: number;
        timeFormat: string;
        /** Format: int32 */
        timeFormatType: number;
        timezoneName: string;
        /** Format: int32 */
        timezoneSeconds: number;
        unitOfDistance: string;
        /** Format: int32 */
        unitOfDistanceType: number;
        unitOfWeight: string;
        /** Format: int32 */
        unitOfWeightType: number;
        /** Format: int64 */
        updateTime: string | number;
        website: string;
        yelp: string;
      };
      /** @description all companies under this account */
      'com.moego.server.business.dto.StaffCompanyDto': {
        address1: string;
        address2: string;
        addressCity: string;
        addressCountry: string;
        addressState: string;
        addressZipcode: string;
        /**
         * Format: int32
         * @deprecated
         */
        appType: number;
        avatarPath: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        businessMode: number;
        businessName: string;
        /** Format: int32 */
        companyId: number;
        companyName: string;
        country: string;
        /** Format: int32 */
        employeeCategory: number;
        /** Format: int32 */
        ownerAccountId: number;
        /** Format: int32 */
        usedStaffCount: number;
        /** Format: int32 */
        usedVanCount: number;
      };
      'com.moego.server.customer.dto.CustomerAddressDto': {
        address1: string;
        address2: string;
        city: string;
        country: string;
        /** Format: int32 */
        customerAddressId: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        isPrimary: number;
        isProfileRequestAddress: boolean;
        lat: string;
        lng: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.customer.dto.CustomerInfoDto': {
        /** Format: int64 */
        accountId: string | number;
        actionState: string;
        address: components['schemas']['com.moego.server.customer.dto.CustomerAddressDto'];
        /** Format: int64 */
        allocateStaffId: string | number;
        /** @description 对应复选框选项： 1-Message 2-email  3-phone call */
        apptReminderByList: number[];
        avatarPath: string;
        /** Format: date-time */
        birthday: string;
        /**
         * Format: int32
         * @deprecated
         */
        businessId: number;
        clientColor: string;
        /** Format: int64 */
        createTime: string | number;
        /** @description customer code(8), all customers have and only have pre-generated one. */
        customerCode: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        expectedServiceDate: string;
        externalId: string;
        firstName: string;
        hasPetParentAppAccount: boolean;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        /** Format: int32 */
        isBlockMessage: number;
        /** Format: int32 */
        isBlockOnlineBooking: number;
        isNewCustomer: boolean;
        isProspectCustomer: boolean;
        /** Format: int32 */
        isRecurring: number;
        /** Format: int32 */
        isUnsubscribed: number;
        lastName: string;
        lastServiceTime: string;
        lifeCycle: string;
        /** @deprecated */
        loginEmail: string;
        notificationSetting: components['schemas']['com.moego.server.customer.dto.CustomerNotificationSettingDTO'];
        phoneNumber: string;
        /** @description preferred business id */
        preferredBusinessId: string;
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        referralSource: string;
        referralSourceDesc: string;
        /** Format: int32 */
        referralSourceId: number;
        /** Format: int32 */
        sendAppAutoMessage: number;
        /** Format: int32 */
        sendAutoEmail: number;
        /** Format: int32 */
        sendAutoMessage: number;
        /** @description 当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效 */
        shareApptIds: number[];
        /** Format: int32 */
        shareApptStatus: number;
        /** Format: int32 */
        shareRangeType: number;
        /**
         * Format: int32
         * @description 不同type时的value
         */
        shareRangeValue: number;
        source: string;
        /** Format: int32 */
        status: number;
        type: string;
        /**
         * Format: int32
         * @deprecated
         */
        unconfirmedReminderBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.customer.dto.CustomerNotificationSettingDTO': {
        brandedAppNotifications: boolean;
        marketingCampaignNotifications: ComMoegoServerCustomerDtoCustomerNotificationSettingDTOMarketingCampaignNotifications[];
        onOff: boolean;
        serviceRelatedNotifications: ComMoegoServerCustomerDtoCustomerNotificationSettingDTOServiceRelatedNotifications[];
      };
      'com.moego.server.customer.dto.CustomerPetDetailDTO': {
        avatarPath: string;
        behavior: string;
        birthday: string;
        breed: string;
        /** Format: int32 */
        breedMix: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        customerId: number;
        deactivateReason: string;
        deleted: boolean;
        emergencyContactName: string;
        emergencyContactPhone: string;
        /**
         * Format: int32
         * @deprecated
         */
        evaluationStatus: number;
        /** Format: int32 */
        expiryNotification: number;
        fixed: string;
        /** Format: int32 */
        gender: number;
        hairLength: string;
        healthIssues: string;
        /** Format: int32 */
        lifeStatus: number;
        passedAway: boolean;
        petAppearanceColor: string;
        petAppearanceNotes: string;
        petBreed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        petPhotoImage: string;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int64 */
        platformPetId: string | number;
        /** Format: int64 */
        playgroupId: string | number;
        /** Format: int32 */
        status: number;
        typeName: string;
        vaccineList: components['schemas']['com.moego.server.customer.dto.VaccineBindingRecordDto'][];
        vetAddress: string;
        vetName: string;
        vetPhone: string;
        weight: string;
      };
      'com.moego.server.customer.dto.VaccineBindingRecordDto': {
        documentUrls: string[];
        expirationDate: string;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        vaccineBindingId: number;
        /** Format: int32 */
        vaccineId: number;
        vaccineName: string;
        /** Format: int32 */
        verifyStatus: number;
      };
      'com.moego.server.grooming.params.BookOnlineCustomerParams': {
        ackMarketingPolicy: boolean;
        address: string;
        address1: string;
        address2: string;
        /** Format: int32 */
        addressId: number;
        answersMap: {
          [key: string]: any;
        };
        /** Format: date-time */
        birthday: string;
        /** @description credit card token for stripe or square */
        chargeToken: string;
        city: string;
        country: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        emergencyContactFirstName: string;
        emergencyContactLastName: string;
        emergencyContactPhone: string;
        firstName: string;
        /** @description true if client supplied */
        hasStripeCard: boolean;
        isProfileRequestAddress: boolean;
        lastName: string;
        lat: string;
        lng: string;
        phoneNumber: string;
        pickupContactFirstName: string;
        pickupContactLastName: string;
        pickupContactPhone: string;
        state: string;
        stripeCustomerId: string;
        zipcode: string;
      };
      'com.moego.server.grooming.params.ob.OBAnonymousParams': {
        /** @description Customized URL domain, demo URL: crazycutepetspa.moego.online */
        domain: string;
        /** @description Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa */
        name: string;
      };
      'com.moego.server.message.dto.BusinessLineDto': {
        a2p: components['schemas']['com.moego.server.message.dto.CompanyA2pStatusDto'];
        phoneNumbers: components['schemas']['com.moego.server.message.dto.PhoneNumberConfigDto'][];
      };
      'com.moego.server.message.dto.CompanyA2pStatusDto': {
        a2pFee: number;
        /** @description No required  Action Required   Pending   Verified   Failed */
        a2pStatus: string;
        /** @description may by null */
        failReason: string;
        isPaid: boolean;
        /** @description 是否需要联系cs支持 */
        needSupport: boolean;
        /**
         * Format: int32
         * @description 1 default 2 Brand Reviewing 3 Campaign Reviewing
         */
        step: number;
        withEin: boolean;
      };
      /** @description email report item for company */
      'com.moego.server.message.dto.EmailReportItem': {
        /** Format: int32 */
        availableEmails: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        companyId: number;
        /** Format: int32 */
        cycleBeginTime: number;
        /** Format: int32 */
        cycleEndTime: number;
        /** Format: int32 */
        purchaseEmailAmount: number;
        /** Format: int32 */
        purchaseEmailLeftover: number;
        /** Format: int32 */
        subscriptionEmailAmount: number;
        /** Format: int32 */
        usedEmails: number;
      };
      /** @description message report item for company */
      'com.moego.server.message.dto.MessageReportItem': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        companyId: number;
        /** Format: int32 */
        cycleBeginTime: number;
        /** Format: int32 */
        cycleEndTime: number;
        /** Format: int32 */
        freeAutoMessage: number;
        /** Format: int32 */
        purchaseAmount: number;
        /** Format: int32 */
        purchaseLeftover: number;
        /** Format: int32 */
        subscriptionAmount: number;
        /** Format: int32 */
        used2wayMessage: number;
        /** Format: int32 */
        usedAutoMessage: number;
        /** Format: int32 */
        usedCall: number;
      };
      'com.moego.server.message.dto.PhoneNumberConfigDto': {
        /** Format: int32 */
        businessId: number;
        phoneNumber: string;
      };
      'com.moego.server.payment.dto.AddBankAccountRequest': {
        accountToken: string;
        stripeAccountId: string;
      };
      'com.moego.server.payment.dto.AttachPaymentMethodRequest': {
        customerCode: string;
        paymentMethodId: string;
      };
      /** @description 对应Square的Address对象，字段逐一映射，详情参考：https://developer.squareup.com/reference/square/objects/Address */
      'com.moego.server.payment.dto.BillingAddress': {
        /** @description addressLine1 */
        address1: string;
        /** @description addressLine2 */
        address2: string;
        /** @description locality */
        city: string;
        /** @description country */
        country: string;
        /** @description administrativeDistrictLevel1 */
        state: string;
        /** @description postalCode */
        zipcode: string;
      };
      /** @description data object */
      'com.moego.server.payment.dto.BillingDataDTO': {
        /** Format: int32 */
        autoRenew: number;
        /**
         * Format: int64
         * @description 当月套餐开始日期
         */
        beginDate: string | number;
        cardInfo: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'];
        /** @description all cards in the company */
        cardList: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'][];
        /**
         * Format: int64
         * @description charge failed time
         */
        chargeFailedTime: string | number;
        /** @description 商家扣费记录 */
        charges: components['schemas']['com.moego.server.payment.dto.ChargeDTO'][];
        /** Format: int32 */
        companyId: number;
        /**
         * Format: int64
         * @description 付费状态下，即首次付费时间
         */
        createTime: string | number;
        currentPlanTier: components['schemas']['com.moego.common.dto.NewPricingLevelDto'];
        downgradePlanData: components['schemas']['com.moego.server.payment.dto.PlanData'];
        emailBilling: components['schemas']['com.moego.server.payment.dto.EmailBillingDTO'];
        /**
         * Format: int64
         * @description 当月套餐结束日期
         */
        expireDate: string | number;
        /** Format: int64 */
        firstChargeFailedTime: string | number;
        isDowngrade: boolean;
        /** @description isSubscription=true : 表示已订阅 */
        isSubscription: boolean;
        msgBilling: components['schemas']['com.moego.server.payment.dto.MessageBillingDTO'];
        /**
         * Format: int32
         * @description 订阅的支付主体 1-company 2-enterprise
         */
        payerType: number;
        /**
         * Format: int32
         * @deprecated
         * @description 公司permissionLevel: 0:free  1:39begin  2:69rising
         */
        permissionLevel: number;
        planTier: components['schemas']['com.moego.common.dto.NewPricingLevelDto'];
        /** Format: int32 */
        planType: number;
        previousPlanTier: components['schemas']['com.moego.common.dto.NewPricingLevelDto'];
        /** @description 套餐价格 */
        price: number;
        /** Format: int32 */
        salonNum: number;
        /**
         * @deprecated
         * @description 订阅套餐显示名字
         */
        title: string;
        /** Format: int64 */
        updateTime: string | number;
        /** Format: int32 */
        vansNum: number;
      };
      'com.moego.server.payment.dto.CanRefundChannel': {
        canRefundAmount: number;
        /** Format: int32 */
        paymentId: number;
        paymentMethod?: string;
      };
      'com.moego.server.payment.dto.CardClientDto': {
        bookOnlineName: string;
        businessDTO: components['schemas']['com.moego.server.business.dto.MoeBusinessDto'];
        customerDTO: components['schemas']['com.moego.server.customer.dto.CustomerInfoDto'];
        paymentSetting: components['schemas']['com.moego.server.payment.dto.PaymentSettingForClientDTO'];
        petsDto: components['schemas']['com.moego.server.customer.dto.CustomerPetDetailDTO'][];
        redirectToOB: boolean;
        /**
         * Format: int32
         * @description 1:valid 2:submitted 3:expired
         */
        submitted: number;
      };
      'com.moego.server.payment.dto.CardDTO': {
        cardBrand: string;
        cardHolderName: string;
        cardType: string;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        expMonth: number;
        /** Format: int32 */
        expYear: number;
        fingerprint: string;
        id: string;
        last4: string;
      };
      'com.moego.server.payment.dto.CardRequestDTO': {
        /** @description url without host: '/customer/client/cof?c=H9DWQUE1'  */
        link: string;
        /** Format: int64 */
        utcTimeStamp: string | number;
      };
      /** @description 商家扣费记录 */
      'com.moego.server.payment.dto.ChargeDTO': {
        /** @description 金额 */
        amount: number;
        /** @description 扣费Id */
        chargeId: string;
        /**
         * Format: int64
         * @description 扣费时间戳
         */
        created: string | number;
        /** @description 扣费单位 */
        currency: string;
        /** @description 扣费描述 */
        description: string;
        /** @description 扣费卡号 */
        paymentMethod: string;
        /** @description 收据链接 */
        receiptUrl: string;
        /** @description 扣费状态 */
        status: string;
      };
      'com.moego.server.payment.dto.CompanyAndBillingDTO': {
        /** @description locations under this company */
        businesses: components['schemas']['com.moego.server.business.dto.StaffCompanyDto'][];
        businessLine: components['schemas']['com.moego.server.message.dto.BusinessLineDto'];
        companyBillingData: components['schemas']['com.moego.server.payment.dto.BillingDataDTO'];
        /** Format: int32 */
        companyId: number;
        companyName: string;
        /** @description company owner id */
        companyOwnerId: string;
        country: string;
        emailReportItem: components['schemas']['com.moego.server.message.dto.EmailReportItem'];
        /** @description enterprise id */
        enterpriseId: string;
        /** @description enterprise owner id */
        enterpriseOwnerId: string;
        /** Format: int32 */
        locationNum: number;
        messageReportItem: components['schemas']['com.moego.server.message.dto.MessageReportItem'];
        /** Format: int32 */
        subscriptionAmount: number;
        /** Format: int32 */
        vansNum: number;
        /**
         * Format: int32
         * @description AS: company 是否迁移 1-迁移前 2-迁移后
         */
        version: number;
      };
      'com.moego.server.payment.dto.CompanyCardDTO': {
        brand: string;
        /** @description stripe generated id */
        cardId: string;
        country: string;
        cvcCheck: string;
        /** Format: int64 */
        expMonth: string | number;
        /** Format: int64 */
        expYear: string | number;
        /** @description 主卡: true is primary, false not primary */
        isDefault: boolean;
        /** @description 卡是否有效 true: 有效， false：无效 */
        isValid: boolean;
        last4: string;
        name: string;
      };
      'com.moego.server.payment.dto.CouponDTO': {
        /** @description 减掉的固定金额。 例如：5   减去5美元 */
        amountOff: number;
        /**
         * @description coupon类型
         * @enum {string}
         */
        businessCategory: ComMoegoServerPaymentDtoCouponDTOBusinessCategory;
        /** @description 前端输入的code  重新返回 */
        code: string;
        /**
         * Format: int32
         * @description coupon code 主键
         */
        id: number;
        /** @description 打折减掉的比例。 例如：20  减去20% */
        percentOff: number;
        /** @description 对应的stripe coupon id */
        stripeCouponId: string;
      };
      'com.moego.server.payment.dto.CreateAccountLinksRequest': {
        returnURL: string;
        stripeAccountId: string;
      };
      'com.moego.server.payment.dto.CreateACHSetupIntentRequest': {
        customerCode: string;
      };
      'com.moego.server.payment.dto.CreateCardRequest': {
        chargeToken: string;
        /** Format: int32 */
        customerId: number;
        ignoreDuplicate: boolean;
        saveCard: boolean;
        stripeAccountId: string;
        stripeCustomerId: string;
      };
      'com.moego.server.payment.dto.CreateCustomerRequest': {
        chargeToken: string;
        /** @description Test Clock 是Stripe 提供的用于测试Subscription的时钟，可以修改时间触发billing cycle 更新。详情参考：https://moego.atlassian.net/l/c/E8TPSm11 测试章节 */
        createTestClock?: boolean;
        /** Format: int32 */
        customerId: number;
        name?: string;
        stripeAccountId?: string;
        stripeName?: string;
      };
      'com.moego.server.payment.dto.CreateIdentitySessionRequest': {
        callbackUrl: string;
      };
      'com.moego.server.payment.dto.CreateStripeAccountRequest': {
        /** Format: int32 */
        businessId: number;
        country: string;
      };
      'com.moego.server.payment.dto.CustomerStripInfoSaveResponse': {
        /** Format: int32 */
        businessId: number;
        cardNumber: string;
        cardType: string;
        /** Format: int32 */
        customerId: number;
        paymentMethodId: string;
        stripeBizAccountId: string;
        stripeCustomerId: string;
      };
      'com.moego.server.payment.dto.DeleteCardForCustomerRequest': {
        cardId: string;
        /** Format: int32 */
        customerId: number;
        stripeAccountId: string;
        stripeCustomerId: string;
      };
      'com.moego.server.payment.dto.DeleteExternalAccountRequest': {
        accountId: string;
        bankAccountId: string;
      };
      /** @description 邮件包购买结果 */
      'com.moego.server.payment.dto.EmailBillingDTO': {
        /**
         * Format: int32
         * @description 邮件 credit 数量
         */
        amount: number;
        /** Format: int32 */
        autoReload: number;
        /**
         * Format: int32
         * @description email支付主体 1-company 2-enterprise
         */
        payerType: number;
        /** @description 对应价格 */
        price: number;
      };
      /** @description data object */
      'com.moego.server.payment.dto.EnterpriseBillingDataDTO': {
        /** Format: int32 */
        autoRenew: number;
        /**
         * Format: int64
         * @description 当月套餐开始日期
         */
        beginDate: string | number;
        /** @description all cards in the enterprise, 复用原有结构 */
        cardList: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'][];
        /**
         * Format: int64
         * @description charge failed time
         */
        chargeFailedTime: string | number;
        /** @description 扣费记录 */
        charges: components['schemas']['com.moego.server.payment.dto.ChargeDTO'][];
        /**
         * Format: int64
         * @description 付费状态下，即首次付费时间
         */
        createTime: string | number;
        downgradePlanData: components['schemas']['com.moego.server.payment.dto.PlanData'];
        /**
         * Format: int64
         * @description 当月套餐结束日期
         */
        expireDate: string | number;
        isDowngrade: boolean;
        /** @description isSubscription=true : 表示已订阅 */
        isSubscription: boolean;
        planTier: components['schemas']['com.moego.common.dto.NewPricingLevelDto'];
        /** Format: int32 */
        planType: number;
        /** @description 套餐价格 */
        price: number;
        /** Format: int32 */
        salonsNum: number;
        /** Format: int64 */
        updateTime: string | number;
        /** Format: int32 */
        vansNum: number;
      };
      'com.moego.server.payment.dto.EnterpriseBillingDTO': {
        billingData: components['schemas']['com.moego.server.payment.dto.EnterpriseBillingDataDTO'];
        enterprise: components['schemas']['com.moego.server.business.dto.EnterpriseDto'];
        /** @description enterprise id */
        enterpriseId: string;
      };
      'com.moego.server.payment.dto.EnterpriseSubscriptionConfigDTO': {
        /** Format: int32 */
        autoRenew: number;
        /** Format: int64 */
        beginDate: string | number;
        /** Format: int64 */
        chargeFailedTime: string | number;
        chargeMsg: string;
        /** Format: int32 */
        chargeStatus: number;
        /** Format: date-time */
        createdAt: string;
        /** Format: int32 */
        currentPlanId: number;
        /** Format: int64 */
        enterpriseId: string | number;
        /** Format: int64 */
        expireDate: string | number;
        /** Format: int32 */
        level: number;
        /** Format: int32 */
        locationNum: number;
        /** Format: int32 */
        nextPlanId: number;
        /** Format: int32 */
        payCompanySubscription: number;
        stripeSubscriptionsId: string;
        /** Format: date-time */
        updatedAt: string;
        /** Format: int32 */
        usedLocationNum: number;
        /** Format: int32 */
        usedVanNum: number;
        /** Format: int32 */
        vansNum: number;
      };
      'com.moego.server.payment.dto.GetSquareTokenResponse': {
        /** @description moego platform integrated application's id */
        appId: string;
        /** Format: int32 */
        businessId: number;
        defaultLocationId: string;
        expiresAt: string;
        merchantId: string;
        /** @description 是否有在square白名单内 */
        squareAccess: boolean;
        /** @description 是否与square connected */
        squareConnected: boolean;
      };
      /** @description 短信购买结果 */
      'com.moego.server.payment.dto.MessageBillingDTO': {
        /**
         * Format: int32
         * @description 短信购买条数
         */
        amount: number;
        /** Format: int32 */
        autoReload: number;
        /**
         * Format: int32
         * @description 已购买短信包 主键ID
         */
        msgPlanId: number;
        /**
         * Format: int32
         * @description message的支付主体 1-company 2-enterprise
         */
        payerType: number;
        /** @description 对应价格 */
        price: number;
      };
      /** @description 短信包价格表 */
      'com.moego.server.payment.dto.MessagePriceDTO': {
        /**
         * Format: int32
         * @description 短信购买条数
         */
        amount: number;
        /** @description 定制价格 */
        customPrice: number;
        /**
         * Format: int32
         * @description 需要回传的主键ID
         */
        id: number;
        /** @description 对应价格 */
        price: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.payment.dto.PaymentAmountSumDto': {
        totalPaidAmount: number;
        totalRefundedAmount: number;
      };
      'com.moego.server.payment.dto.PaymentDTO': {
        amount: number;
        /** Format: int32 */
        businessId: number;
        /** @description current desc: square支付不成功时，记录失败原因 */
        cancelReason: string;
        /** @description Credit/Debit etc. */
        cardFunding: string;
        cardNumber: string;
        cardType: string;
        checkNumber: string;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        customerName: string;
        description: string;
        expMonth: string;
        expYear: string;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        isOnline: boolean;
        /** @description 当前支付记录是否为prepay */
        isPrepay: boolean;
        locationId: string;
        method: string;
        /** Format: int32 */
        methodId: number;
        module: string;
        /** Format: int64 */
        orderPaymentId: string | number;
        paidBy: string;
        paymentMethod: string;
        processingFee: number;
        refundedAmount: number;
        refunds: components['schemas']['com.moego.server.payment.dto.RefundDTO'][];
        signature: string;
        /** Format: int32 */
        squarePaymentMethod: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        stripeIntentId: string;
        /** Format: int32 */
        stripePaymentMethod: number;
        totalCollected: number;
        /** Format: int64 */
        transactionId: string | number;
        /** Format: int64 */
        updateTime: string | number;
        vendor: string;
      };
      'com.moego.server.payment.dto.PaymentIntentResponse': {
        intendId: string;
        secretKey: string;
      };
      'com.moego.server.payment.dto.PaymentListDto': {
        /** Format: int32 */
        paymentCount: number;
        paymentList: components['schemas']['com.moego.server.payment.dto.PaymentDTO'][];
      };
      'com.moego.server.payment.dto.PaymentProcessingFeeDTO': {
        /** @description 计算得出的processing fee金额 */
        processingFee: number;
      };
      'com.moego.server.payment.dto.PaymentSettingDTO': {
        /** Format: int32 */
        allowCustomRate: number;
        /** Format: int32 */
        autoCancelFeeByClient: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        cardAuthEnable: number;
        /** Format: int32 */
        closeCustomRate: number;
        /** Format: date-time */
        createTime: string;
        customizedFeeName: string;
        /** @description 定制json报文 */
        customTipping: string;
        /**
         * Format: int32
         * @description payment setting primary id
         */
        id: number;
        /** Format: int32 */
        onlineFeeCents: number;
        onlineFeeRate: number;
        /**
         * Format: int32
         * @description 提前多久进行扣款,单位：hour
         */
        preAuthBspd: number;
        /** Format: int32 */
        primaryPayType: number;
        /**
         * Format: date-time
         * @description update time of the primaryPayType field
         */
        primaryPayTypeUpdateTime: string;
        /** Format: int32 */
        processingFeeCalMethod: number;
        /** Format: int32 */
        processingFeePayBy: number;
        processingFeeSignature: string;
        /** Format: int32 */
        readerFeeCents: number;
        readerFeeRate: number;
        /** Format: int64 */
        signatureTime: string | number;
        /** Format: int32 */
        skipTipping: number;
        /** Format: date-time */
        updateTime: string;
      };
      'com.moego.server.payment.dto.PaymentSettingForClientDTO': {
        /** Format: int32 */
        cardAuthEnable: number;
        /** @description 自定义费名 */
        customizedFeeName: string;
        /** @description 定制json报文 */
        customTipping: string;
        /** Format: int32 */
        preAuthBspd: number;
        /** Format: int32 */
        processingFeePayBy: number;
        /** Format: int32 */
        skipTipping: number;
      };
      'com.moego.server.payment.dto.PlanData': {
        planTier: components['schemas']['com.moego.common.dto.NewPricingLevelDto'];
        price: number;
        /** Format: int32 */
        salonNum: number;
        /** Format: int32 */
        vanNum: number;
      };
      'com.moego.server.payment.dto.PreAuthCardDto': {
        preAuthCardNumber: string;
        preAuthPaymentMethod: string;
      };
      'com.moego.server.payment.dto.PreAuthDTO': {
        cardFunding: string;
        cardNumber: string;
        cardType: string;
        /** Format: int32 */
        customerId: number;
        expMonth: string;
        expYear: string;
        feesDetail: components['schemas']['com.moego.server.payment.dto.PreAuthFeesDTO'];
        inBSPD: boolean;
        isCapture: boolean;
        method: string;
        /** Format: int32 */
        paymentId: number;
        preAuthAmount: number;
        preAuthCardNumber: string;
        preAuthFailedMessage: string;
        /** Format: int64 */
        preAuthFinishTime: string | number;
        /** Format: int32 */
        preAuthId: number;
        preAuthPaymentMethod: string;
        /** Format: int32 */
        preAuthStatus: number;
        /** Format: int64 */
        preAuthTime: string | number;
        /** Format: int32 */
        ticketId: number;
      };
      'com.moego.server.payment.dto.PreAuthFeesDTO': {
        bookingFee: number;
        convenienceFee: number;
        tipsAmount: number;
      };
      'com.moego.server.payment.dto.ReaderActionInfoResp': {
        paymentId: string;
        paymentStatus: string;
      };
      'com.moego.server.payment.dto.ReaderAuthCodeResponse': {
        authCode: string;
        /** Format: int32 */
        businessId: number;
        expiresAt: string;
        locationId: string;
      };
      'com.moego.server.payment.dto.ReaderStatusDTO': {
        message: string;
        status: string;
      };
      'com.moego.server.payment.dto.RefundChannelDTO': {
        channelList: components['schemas']['com.moego.server.payment.dto.CanRefundChannel'][];
        /** Format: int32 */
        invoiceId: number;
        isCombination: boolean;
        refundAmount: number;
      };
      /** @description 商家扣费记录 */
      'com.moego.server.payment.dto.RefundDTO': {
        amount: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        error: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        method: string;
        /** Format: int32 */
        methodId: number;
        module: string;
        /** Format: int32 */
        originPaymentId: number;
        reason: string;
        /** Format: int32 */
        refundId: number;
        /** Format: int64 */
        refundOrderPaymentId: string | number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        stripeRefundId: string;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.payment.dto.SetDefaultExternalAccountRequest': {
        accountId: string;
        bankAccountId: string;
      };
      'com.moego.server.payment.dto.SetupIntentDTO': {
        clientSecret: string;
        setupIntentId: string;
      };
      'com.moego.server.payment.dto.SmartTipConfigDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: date-time */
        createTime: string;
        /** Format: int32 */
        enable: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        lowerPreferredTip: number;
        lowerTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** Format: int32 */
        lowerTipType: number;
        readerLowerTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        readerUpperTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** @description Smart tip区分低档和高档的阈值，大于0，最多2位小数 */
        threshold: number;
        /** Format: date-time */
        updateTime: string;
        /** Format: int32 */
        upperPreferredTip: number;
        upperTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** Format: int32 */
        upperTipType: number;
      };
      'com.moego.server.payment.dto.square.GetSquareAuthUrlResponse': {
        applicationId: string;
        /** @description URL for biz to get authorization of biz's square account */
        authUrl: string;
        permissions: string[];
        state: string;
      };
      'com.moego.server.payment.dto.square.SetSquareDefaultLocationResponse': {
        /** Format: int32 */
        businessId: number;
        locationId: string;
      };
      'com.moego.server.payment.dto.square.SquareCard': {
        billingAddress: components['schemas']['com.moego.server.payment.dto.BillingAddress'];
        cardBrand: string;
        cardHolderName: string;
        cardType: string;
        /** Format: int32 */
        expMonth: number;
        /** Format: int32 */
        expYear: number;
        id: string;
        last4: string;
      };
      'com.moego.server.payment.dto.square.SquareCreateCOFResponse': {
        card: components['schemas']['com.moego.server.payment.dto.square.SquareCard'];
        /** Format: int32 */
        customerId: number;
        squareCustomerId: string;
      };
      'com.moego.server.payment.dto.square.SquareCreateCustomerRequest': {
        billingAddress?: components['schemas']['com.moego.server.payment.dto.BillingAddress'];
        /** @description The full name printed on the credit card */
        cardHolderName?: string;
        /** @description Card nonces are generated by the Square payment form when customers enter their card information */
        cardNonce: string;
        /**
         * Format: int32
         * @description moego customer primary id
         */
        customerId: number;
      };
      'com.moego.server.payment.dto.square.SquareCreateCustomerResponse': {
        card: components['schemas']['com.moego.server.payment.dto.square.SquareCard'];
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        lastName: string;
        phoneNumber: string;
        squareCustomerId: string;
      };
      'com.moego.server.payment.dto.square.SquareCustomerInfo': {
        cards: components['schemas']['com.moego.server.payment.dto.square.SquareCard'][];
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        lastName: string;
        phoneNumber: string;
        squareCustomerId: string;
      };
      'com.moego.server.payment.dto.square.SquareDevice': {
        /** @description generated by square server */
        code?: string;
        createdAt?: string;
        /** @description generated by square server */
        deviceId?: string;
        id?: string;
        /** @description set by backend, always be moego default location id */
        locationId?: string;
        /** @description a user-defined name for the device code. */
        name: string;
        /** @description set by backend, always be TERMINAL_API */
        productType?: string;
        status?: string;
      };
      'com.moego.server.payment.dto.square.SquareDeviceCodeResponse': {
        code: string;
        createdAt: string;
        locationId: string;
        name: string;
        productType: string;
        status: string;
      };
      'com.moego.server.payment.dto.square.SquareLocation': {
        address1: string;
        address2: string;
        city: string;
        country: string;
        createdAt: string;
        currency: string;
        id: string;
        isDefault: boolean;
        merchantId: string;
        /** @description "location nickname" in the Square Dashboard, which is the merchant-facing moniker */
        name: string;
        state: string;
        status: string;
        zipcode: string;
      };
      'com.moego.server.payment.dto.square.SquarePaymentRequest': {
        /** @description 扣费金额： ex：1.23  当currency=USD时表示消费1.23$ */
        amount: number;
        /** Format: int32 */
        businessId?: number;
        /** @description useCOF=true: 'ccof:xxxxxxx' ;useCOF=false: 'cnon:xxxxxxxx';for reader sdk and terminal: set this param empty string */
        cardNonce: string;
        /** @description set by back end; 取值：USD, AUD, CAD, SGD, EUR, GBP or JPY */
        currency?: string;
        /** Format: int32 */
        customerId?: number;
        description?: string;
        /** @description required for terminal charge : get device ids from /payment/square/terminal/devices */
        deviceId?: string;
        /** Format: int32 */
        groomingId?: number;
        /** Format: int32 */
        invoiceId: number;
        /** Format: int32 */
        isDeposit?: number;
        /** @description set by back end */
        isOnline?: boolean;
        /** @description 是否为Square POS */
        isSquarePos?: boolean;
        /** @description specified by Backend with moego default location */
        locationId?: string;
        /** @description specified by Backend for square transaction history */
        merchant?: string;
        /** @description grooming, retail */
        module?: string;
        paidBy?: string;
        /** @description saved s3 pic url */
        signature?: string;
        /** @description 通过allow_tipping 参数设置：https://developer.squareup.com/reference/square/objects/TipSettings */
        skipTipping?: boolean;
        /** @description useCOF=true时，前端需提供 */
        squareCustomerId?: string;
        /** @description required for reader payment， square payment 对象id ex: GQTFp1ZlXdpoW4o6eGiZhbjosiDFf */
        squarePaymentId?: string;
        /**
         * Format: int32
         * @description 通过tokenStaffId传入，前端无需提供
         */
        staffId?: number;
        /** @description tips amount */
        tipsAmount?: number;
        /** @description 是否使用card on file， useCOF=null 按false处理 */
        useCOF?: boolean;
      };
      /** @description for reader sdk */
      'com.moego.server.payment.dto.square.SquareReaderPaymentSyncParam': {
        /**
         * Format: int32
         * @description moego payment table primary id
         */
        primaryId: number;
        /** @description reader create checkout 后返回结果中的transactionId， 例子：HEOeaVrkrJ3xcPONuKVIHW7eV */
        transactionId: string;
      };
      'com.moego.server.payment.dto.square.SquareTakePaymentResponse': {
        amount: number;
        cartLast4: string;
        createdAt: string;
        currency: string;
        locationId: string;
        paymentId: string;
        /** Format: int32 */
        primaryId: number;
        sourceType: string;
        status: string;
      };
      'com.moego.server.payment.dto.square.SquareTokenUpdateResponse': {
        /** Format: int32 */
        businessId: number;
        expiresAt: string;
        merchantId: string;
      };
      'com.moego.server.payment.dto.square.TerminalChargeResponse': {
        amount: number;
        /** @description generated by square server */
        checkoutId: string;
        currency: string;
        /** @description         Getter for DeadlineDuration.
         *        The duration as an RFC 3339 duration, after which the checkout will be automatically
         *        canceled. TerminalCheckouts that are `PENDING` will be automatically `CANCELED` and have a
         *        cancellation reason of `TIMED_OUT`. Default: 5 minutes from creation Maximum: 5 minutes */
        deadlineDuration: string;
        deviceId: string;
        /**
         * Format: int32
         * @description generated by moego server
         */
        moePaymentId: number;
        status: string;
      };
      'com.moego.server.payment.dto.stripe.HardwareOrderDTO': {
        /** @description hardwareDiscount = m2Amount * 10% + smartReaderAmount * 10% or zero */
        hardwareDiscount: number;
        /** @description stripe returned value when previewing order */
        hardwareTax: number;
        /** @description m2Amount = m2Quantity * $249 */
        m2Amount: number;
        /** @description 下单成功后, 会返回订单号 */
        orderId: string;
        /** @description stripe returned value */
        shippingFee: number;
        /** @description smartReaderAmount = smartReaderQuantity * $59 */
        smartReaderAmount: number;
        /** @description totalAmount = m2Amount + smartReaderAmount + shippingFee + hardwareTax - hardwareDiscount */
        totalAmount: number;
      };
      'com.moego.server.payment.dto.stripe.HardwareOrderParams': {
        /** Format: int64 */
        accountId?: string | number;
        address: components['schemas']['com.moego.server.payment.dto.stripe.HardwareOrderParams$Address'];
        /**
         * @description bundle sale type: 目前有UNIQUE_LINK， SALE
         * @enum {string}
         */
        bundleSaleType?: ComMoegoServerPaymentDtoStripeHardwareOrderParamsBundleSaleType;
        /** @description bundle sale type 非 SALE 时需要填写 */
        bundleSaleUuid?: string;
        /** @description Stripe token，未加卡商家仅购买硬件时使用 */
        chargeToken?: string;
        /** Format: int32 */
        companyId: number;
        companyName: string;
        email: string;
        /** @description 如果为null, 不做校验; 非空字符串校验合法性, 目前固定值 */
        hardwareCouponCode?: string;
        /**
         * Format: int32
         * @description stripe_m2/bluetooth reader 购买个数
         */
        m2Quantity: number;
        phoneNumber: string;
        /** @description The purchase order number will appear on the packing slip, shipping label, and monthly billing invoice. No more than 15 characters. */
        poNumber?: string;
        /** @description receiver name */
        recipientName: string;
        /** @description get all available shipping methods from stripe, ref api: /payment/hardware/shippingMethods */
        shippingMethod: string;
        /**
         * Format: int32
         * @description bbpos_wisepos_e/smart reader 购买个数
         */
        smartReaderQuantity: number;
      };
      /** @description The shipping address for the order. Required if any of the SKUs are physical goods. ref: https://stripe.com/docs/api/terminal/hardware_orders/preview#preview_terminal_hardware_order-shipping-address */
      'com.moego.server.payment.dto.stripe.HardwareOrderParams$Address': {
        city?: string;
        /** @description 目前固定为US, ref https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2 */
        country: string;
        line1: string;
        /** @description optional property */
        line2?: string;
        postalCode: string;
        state?: string;
      };
      'com.moego.server.payment.dto.stripe.LocationDto': {
        address: Stripe.Address;
        displayName: string;
        /** @description stripe location id */
        id: string;
      };
      'com.moego.server.payment.dto.stripe.LocationParam': {
        /**
         * Format: int32
         * @description Frontend should omit this; auto set by controller
         */
        businessId?: number;
        city: string;
        /**
         * Format: int64
         * @description Frontend should omit this; auto set by controller
         */
        companyId?: string | number;
        /** @description 国家码需要满足ISO-3166 https://stripe.com/docs/api/terminal/locations/object#terminal_location_object-address-country */
        country: string;
        displayName: string;
        line1: string;
        line2?: string;
        locationId?: string;
        postalCode: string;
        state: string;
      };
      'com.moego.server.payment.dto.stripe.PaymentMethodDto': {
        cardNumber: string;
        cardType: string;
        expMonth: string;
        expYear: string;
        /** @description true: 没有保存过， false： 保存过同一张卡 */
        newCard: boolean;
        /** @description stripe payment method id(card id)  */
        paymentMethodId: string;
      };
      'com.moego.server.payment.dto.stripe.PaymentMethodParam': {
        paymentMethodId: string;
      };
      'com.moego.server.payment.dto.stripe.ReaderDto': {
        deviceType: string;
        id: string;
        label: string;
        serialNumber: string;
        status: string;
      };
      'com.moego.server.payment.dto.stripe.ReaderParam': {
        label?: string;
        locationId: string;
        registrationCode: string;
      };
      'com.moego.server.payment.dto.stripe.ShippingMethod': {
        country: string;
        id: string;
        name: string;
        object: string;
        status: string;
        unavailableAfter: string;
      };
      'com.moego.server.payment.dto.stripe.ShippingMethodCollection': {
        data: components['schemas']['com.moego.server.payment.dto.stripe.ShippingMethod'][];
        object: string;
      };
      'com.moego.server.payment.dto.SubscriptionPlanDTO': {
        /**
         * Format: int32
         * @description corresponding unit for this stripe plan
         */
        businessNum: number;
        /** Format: int32 */
        businessType: number;
        description: string;
        /**
         * Format: int32
         * @description primary id
         */
        id: number;
        /** Format: int32 */
        isNewPricing: number;
        /** Format: int32 */
        level: number;
        planName: string;
        planTier: components['schemas']['com.moego.common.dto.NewPricingLevelDto'];
        /** Format: int32 */
        planType: number;
        /** @description unit price */
        price: number;
        stripePlanId: string;
        title: string;
      };
      'com.moego.server.payment.dto.TestDTO': {
        /** Format: int32 */
        id: number;
        name: string;
        test: string;
      };
      'com.moego.server.payment.params.BookOnlinePayParams': {
        /** @description 是否在支付金额加入processing fee pay by client */
        addProcessingFee?: boolean;
        amount: number;
        amountWithoutTips?: number;
        /** @description booking fee amount */
        bookingFeeAmount?: number;
        /**
         * @deprecated
         * @description 支付请求的来源是OB时需要传的businessName
         */
        businessName?: string;
        cardNumber?: string;
        cardType?: string;
        /** @description for credit card */
        chargeToken?: string;
        checkNumber?: string;
        customerData?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        /** Format: int32 */
        customerId?: number;
        description?: string;
        expMonth?: string;
        expYear?: string;
        guid?: string;
        /** Format: int32 */
        isDeposit?: number;
        isOnline?: boolean;
        /** @description set when Stripe reader/terminal calling */
        locationId?: string;
        method?: string;
        /** Format: int32 */
        methodId?: number;
        module?: string;
        /** Format: int64 */
        orderId?: string | number;
        paidBy?: string;
        paymentIntentId?: string;
        readerId?: string;
        saveCard?: boolean;
        signature?: string;
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        stripePaymentMethod?: number;
        stripePaymentMethodId?: string;
        /** @description tips amount */
        tipsAmount?: number;
      };
      'com.moego.server.payment.params.CancelSubParams': {
        reason: string;
        /**
         * Format: int32
         * @deprecated
         */
        reasonType: number;
        reasonTypeStr: string;
      };
      'com.moego.server.payment.params.CardParams': {
        /** @description 通过卡信息去stripe平台获取到的token值 */
        stripeToken: string;
      };
      'com.moego.server.payment.params.CardRequestParams': {
        email?: string;
        firstName?: string;
        lastName?: string;
        phone?: string;
        /** @description 通过卡信息去stripe平台获取到的token值 */
        stripeToken: string;
      };
      'com.moego.server.payment.params.CheckRefundChannelParams': {
        /** Format: int32 */
        businessId: number;
        changedAmount: number;
        /** Format: int32 */
        invoiceId: number;
      };
      'com.moego.server.payment.params.CreatePaymentParams': {
        /** @description 是否在支付金额加入processing fee pay by client */
        addProcessingFee?: boolean;
        amount: number;
        amountWithoutBookingFee?: number;
        amountWithoutTips?: number;
        /** @deprecated */
        bookingFeeAmount?: number;
        /** @description 支付请求的来源是OB时需要传的businessName */
        businessName?: string;
        cardNumber?: string;
        cardType?: string;
        /** @description for credit card */
        chargeToken?: string;
        checkNumber?: string;
        customerData?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        /** Format: int32 */
        customerId?: number;
        description?: string;
        expMonth?: string;
        expYear?: string;
        fromPreAuth?: boolean;
        guid?: string;
        /** Format: int32 */
        invoiceId: number;
        isCancellationFee?: boolean;
        /** Format: int32 */
        isDeposit?: number;
        isOnline?: boolean;
        /** @description set when Stripe reader/terminal calling */
        locationId?: string;
        method?: string;
        /** Format: int32 */
        methodId?: number;
        module?: string;
        paidBy?: string;
        paymentIntentId?: string;
        /** Format: int32 */
        preAuthId?: number;
        /** Format: int64 */
        rawCreateTime?: string | number;
        rawId?: string;
        /** Format: int64 */
        rawUpdateTime?: string | number;
        readerId?: string;
        saveCard?: boolean;
        signature?: string;
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        stripePaymentMethod?: number;
        stripePaymentMethodId?: string;
        /** @description tips amount */
        tipsAmount?: number;
      };
      'com.moego.server.payment.params.CreateRefundParams': {
        amount: number;
        /** Format: int32 */
        paymentId: number;
        /** @description refund reason, moego table column size is 1023, but square refund api param(reason) limit is 192 */
        reason?: string;
        /** Format: int32 */
        refundOrderPaymentId?: number;
      };
      'com.moego.server.payment.params.CustomerStripInfoRequest': {
        addAch?: boolean;
        chargeToken: string;
        /** Format: int32 */
        customerId: number;
        ignoreDuplicateCard?: boolean;
      };
      /** @description 邮件包购买 */
      'com.moego.server.payment.params.EmailBuyParams': {
        /** Format: int32 */
        accountId?: number;
        /** Format: int32 */
        autoReload?: number;
        /**
         * Format: int32
         * @description 公司主键
         */
        companyId: number;
        /**
         * Format: int32
         * @description 要购买的 credit 数量，默认 1 个
         */
        credits?: number;
      };
      'com.moego.server.payment.params.GetByCustomerIdParams': {
        /** Format: int32 */
        customerId: number;
      };
      /** @description 短信包购买 */
      'com.moego.server.payment.params.MsgBuyParams': {
        /** Format: int32 */
        accountId?: number;
        /** Format: int32 */
        autoReload?: number;
        /**
         * Format: int32
         * @description 公司主键
         */
        companyId: number;
        /**
         * Format: int32
         * @description 需要购买的短信包id
         */
        msgPlanId: number;
      };
      'com.moego.server.payment.params.PaymentSigRequest': {
        /**
         * Format: int32
         * @description moego payment table primary id
         */
        primaryId: number;
        signature?: string;
      };
      'com.moego.server.payment.params.PreAuthRetryParams': {
        preAuthCardNumber?: string;
        preAuthPaymentMethod?: string;
        /** Format: int32 */
        retryType: number;
        /** Format: int32 */
        ticketId: number;
      };
      'com.moego.server.payment.params.SmartTipConfigParams': {
        /** Format: int32 */
        enable: number;
        /** Format: int32 */
        lowerPreferredTip: number;
        lowerTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** Format: int32 */
        lowerTipType: number;
        readerLowerTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        readerUpperTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** @description Smart tip区分低档和高档的阈值，大于0，最多2位小数 */
        threshold: number;
        /** Format: int32 */
        upperPreferredTip: number;
        upperTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** Format: int32 */
        upperTipType: number;
      };
      /** @description 订阅参数 */
      'com.moego.server.payment.params.SubscriptionParams': {
        /** Format: int32 */
        accountId?: number;
        chargeToken?: string;
        /** Format: int32 */
        companyId: number;
        /** @description Test Clock 是Stripe 提供的用于测试Subscription的时钟，可以修改时间触发billing cycle 更新。详情参考：https://moego.atlassian.net/l/c/E8TPSm11 测试章节 */
        createTestClock?: boolean;
        /**
         * Format: int32
         * @description locations number
         */
        locationsNum: number;
        /**
         * Format: int32
         * @description admin 使用，期望根据此参数获得的短信条数
         */
        newBusinessNums?: number;
        /**
         * Format: int32
         * @description moe_price_plan_conf table的主键ID
         */
        planId?: number;
        /** @description 根据客服提供给用户的优惠码， 从/payment/subscription/coupon接口查询stripe coupon code id；根据prefix判断兼容referral功能，referralCode复用该字段 */
        stripeCouponId?: string;
        /** @description stripe plan id, 从/payment/subscription/plans接口下发 */
        stripePlanId: string;
        /**
         * Format: int32
         * @description mobile vans number
         */
        vansNum: number;
        /** @description assigned from backendServer */
        vanStripePlanId?: string;
      };
      'com.moego.server.payment.params.TestParams': {
        /** Format: int32 */
        userId: number;
      };
      /** @description Smart tip打开时Reader单独的高档位Tip配置 */
      'com.moego.server.payment.params.TipMap': {
        amountConfig: components['schemas']['com.moego.server.payment.params.TipMap$AmountTipMap'];
        percentageConfig: components['schemas']['com.moego.server.payment.params.TipMap$PercentageTipMap'];
      };
      /** @description by amount 3个tip配置 */
      'com.moego.server.payment.params.TipMap$AmountTipMap': {
        high: number;
        low: number;
        medium: number;
      };
      /** @description by percentage 3个tip配置 */
      'com.moego.server.payment.params.TipMap$PercentageTipMap': {
        /** Format: int32 */
        high: number;
        /** Format: int32 */
        low: number;
        /** Format: int32 */
        medium: number;
      };
      'com.moego.server.payment.params.UpdateEnterpriseSubscriptionConfigParams': {
        /** Format: int64 */
        enterpriseId: string | number;
        /** Format: int32 */
        locationNum?: number;
        /** Format: int32 */
        vansNum?: number;
      };
      'com.moego.server.payment.service.params.ClientTestClockParam': {
        /**
         * Format: int32
         * @description 针对billing cycle 需要提前的天数，最多提前两个月
         */
        advanceDays: number;
        /** Format: int32 */
        customerId: number;
      };
      /** @description new pricing 订阅参数 */
      'com.moego.server.payment.service.params.EnterpriseSubscriptionUpdateParam': {
        /** Format: int64 */
        enterpriseId: string | number;
        salonPlan: components['schemas']['com.moego.server.payment.service.params.PlanUnit'];
        /** @description 根据客服提供给用户的优惠码， 从/payment/subscription/coupon接口查询stripe coupon code id */
        stripeCouponId?: string;
        vanPlan: components['schemas']['com.moego.server.payment.service.params.PlanUnit'];
      };
      /** @description mobile vans details */
      'com.moego.server.payment.service.params.PlanUnit': {
        /** Format: int32 */
        count: number;
        stripePlanId: string;
      };
      'com.moego.server.payment.service.params.StripeTestClockParam': {
        /**
         * Format: int32
         * @description 针对billing cycle 需要提前的天数，最多提前两个月
         */
        advanceDays: number;
        /** Format: int32 */
        companyId: number;
      };
      /** @description new pricing 订阅参数 */
      'com.moego.server.payment.service.params.SubscriptionCreateParam': {
        /** Format: int32 */
        accountId?: number;
        chargeToken?: string;
        /** Format: int32 */
        companyId: number;
        /** @description Test Clock 是Stripe 提供的用于测试Subscription的时钟，可以修改时间触发billing cycle 更新。详情参考：https://moego.atlassian.net/l/c/E8TPSm11 测试章节 */
        createTestClock?: boolean;
        /** @description platform sales code */
        salesCode?: string;
        salonPlan: components['schemas']['com.moego.server.payment.service.params.PlanUnit'];
        /** @description 根据客服提供给用户的优惠码， 从/payment/subscription/coupon接口查询stripe coupon code id；根据prefix判断兼容referral功能，referralCode复用该字段 */
        stripeCouponId?: string;
        /** @description subscription term type, monthly / annual */
        termType?: string;
        vanPlan: components['schemas']['com.moego.server.payment.service.params.PlanUnit'];
      };
      /** @description new pricing 订阅参数 */
      'com.moego.server.payment.service.params.SubscriptionUpdateParam': {
        allowCrossVersionDowngrade?: boolean;
        /** Format: int32 */
        companyId: number;
        salonPlan: components['schemas']['com.moego.server.payment.service.params.PlanUnit'];
        /** @description 根据客服提供给用户的优惠码， 从/payment/subscription/coupon接口查询stripe coupon code id */
        stripeCouponId?: string;
        vanPlan: components['schemas']['com.moego.server.payment.service.params.PlanUnit'];
      };
      'com.moego.server.payment.TapToPaySettingDTO': {
        /** Format: int32 */
        discountRound?: number;
        /** Format: int32 */
        extraFeeInCents?: number;
        /** Format: int32 */
        remainDiscountDays?: number;
        /** Format: int32 */
        remainDiscountTimesPerMonth?: number;
        ttpEnable: boolean;
        ttpLinkedEnable?: boolean;
      };
      'com.moego.server.payment.web.dto.ACHDetailResp': {
        status: string;
      };
      'com.moego.server.payment.web.dto.ACHDTO': {
        accountName?: string;
        accountNumber: string;
        /** Format: int32 */
        accountType: number;
        email?: string;
        routingNumber: string;
      };
      'com.moego.server.payment.web.dto.ACHMandateConfirmResp': {
        /** Format: int32 */
        paymentId: number;
        redirectUrl: string;
        status: string;
        verifyType: string;
      };
      'com.moego.server.payment.web.dto.BusinessHasValidAccountResponse': {
        /** Format: int32 */
        businessId: number;
        hasValidAccount: boolean;
      };
      'com.moego.server.payment.web.dto.FeatureRequireConfigDto': {
        code: string;
        /**
         * Format: int32
         * @description 需要的最小premium type
         */
        needPremiumType: number;
      };
      'com.moego.server.payment.web.dto.IdentityStatusResponse': {
        /**
         * Format: int64
         * @description 当前identity session 的过期时间
         */
        expireTime: string | number;
        /** @description owner是否需要identity true false */
        needIdentity: boolean;
        /**
         * Format: int32
         * @description owner staff id
         */
        ownerStaffId: number;
        /** @description (only owner访问时才会下发)session 状态：requires_input processing verified canceled */
        sessionStatus: string;
        /** @description 当前identity session 访问地址 */
        sessionUrl: string;
      };
      'com.moego.server.payment.web.dto.ListFranchiseeBillDatesAndCardsResp': {
        franchiseeBillDatesAndCards: components['schemas']['com.moego.server.payment.web.dto.ListFranchiseeBillDatesAndCardsResp$FranchiseeBillDatesAndCards'][];
      };
      'com.moego.server.payment.web.dto.ListFranchiseeBillDatesAndCardsResp$FranchiseeBillDatesAndCards': {
        /** Format: int32 */
        billDate?: number;
        cards?: components['schemas']['com.moego.server.payment.dto.CompanyCardDTO'][];
        /** Format: int64 */
        companyId: string | number;
      };
      'com.moego.server.payment.web.dto.PayoutDetails': {
        /** Format: int64 */
        amount: string | number;
        /** Format: int64 */
        arrivalTime: string | number;
        /** Format: int64 */
        createTime: string | number;
        currency: string;
        description: string;
        hasMore: boolean;
        id: string;
        payoutSummary: components['schemas']['com.moego.server.payment.web.dto.PayoutSummary'];
        platform: string;
        /** Format: int32 */
        size: number;
        status: string;
        transactions: components['schemas']['com.moego.server.payment.web.dto.PayoutTransaction'][];
      };
      'com.moego.server.payment.web.dto.PayoutListSummaryResp': {
        grossSales: number;
        netSales: number;
        totalCollected: number;
        totalPayout: number;
      };
      'com.moego.server.payment.web.dto.PayoutResp': {
        /**
         * Format: int64
         * @description 当前的payout schedule delay days
         */
        delayDays: string | number;
        /**
         * @description 最新的payout schedule status
         * @enum {string}
         */
        payoutScheduleStatus: ComMoegoServerPaymentWebDtoPayoutRespPayoutScheduleStatus;
      };
      'com.moego.server.payment.web.dto.PayoutSummary': {
        /**
         * Format: int64
         * @description adjustment
         */
        adjustment: string | number;
        /**
         * Format: int64
         * @description sum(amount) = totalCollected + adjustment + returns
         */
        amountsSum: string | number;
        /** @description sum of booking fee */
        bookingFees: number;
        /** @description capital payouts */
        capitalPayouts: components['schemas']['com.moego.server.payment.web.dto.PayoutSummaryRepayment'][];
        /** @description sum of convenience_fee */
        convenienceFee: number;
        /** @description sum of discounts = sum(discount_amount) */
        discounts: number;
        /**
         * Format: int64
         * @description sum of fee in payout (including booking fee, fee adjustment)
         */
        fees: string | number;
        /** @description sum of service price and add on and convenienceFee = sum(subTotalAmount + convenience) */
        grossSales: number;
        /**
         * Format: int64
         * @description sum of net正常情况下： transfer 应该等于 nets
         */
        nets: string | number;
        /** @description grossSales + returns - discounts */
        netSales: number;
        /** @description repayment object, 记录还款金额 */
        repayments: components['schemas']['com.moego.server.payment.web.dto.PayoutSummaryRepayment'][];
        /** @description sum of refunds = sum(refunded_amount), 自动去除bookingFee, 从 stripe 拉取， 负值 */
        returns: number;
        /** @description sum of tax = sum(tax_amount) */
        tax: number;
        /** @description sum of tips = sum(tip_amount */
        tips: number;
        /** @description totalCollected = netSales + tax + tips + convenienceFee */
        totalCollected: number;
        /**
         * Format: int64
         * @description transfer = total collected - fees + adjustment = nets
         */
        transfer: string | number;
      };
      /** @description capital payouts */
      'com.moego.server.payment.web.dto.PayoutSummaryRepayment': {
        /**
         * Format: int64
         * @description repayment amount
         */
        amount: string | number;
        /** @description repayment channel */
        channel: string;
      };
      'com.moego.server.payment.web.dto.PayoutTransaction': {
        /** Format: int64 */
        amount: string | number;
        /** Format: int64 */
        availableTime: string | number;
        /** Format: int64 */
        createTime: string | number;
        currency: string;
        /** Format: int64 */
        customerId: string | number;
        description: string;
        /** Format: int64 */
        fee: string | number;
        id: string;
        /** Format: int64 */
        net: string | number;
        notes: string;
        /** Format: int64 */
        orderId: string | number;
        /** Format: int64 */
        paymentId: string | number;
        paymentIntentId: string;
        reason: string;
        refundId: string;
        /** Format: int32 */
        refundPrimaryId: number;
        serviceType: string;
        sourceObject: string;
        sourceType: string;
        status: string;
        /** Format: int64 */
        targetId: string | number;
        transferredBy: string;
        type: string;
      };
      'com.moego.server.payment.web.dto.PayoutView': {
        accountId: string;
        accountLast4: string;
        accountName: string;
        /** Format: int64 */
        amount: string | number;
        /** Format: int64 */
        arrivalTime: string | number;
        bankName: string;
        /** Format: int64 */
        createTime: string | number;
        currency: string;
        description: string;
        /** Format: int64 */
        fee: string | number;
        id: string;
        method: string;
        /** Format: int64 */
        net: string | number;
        sourceType: string;
        statementDescriptor: string;
        status: string;
      };
      'com.moego.server.payment.web.dto.PayoutViewList': {
        /** Format: int64 */
        endCreatedDate: string | number;
        hasMore: boolean;
        payouts: components['schemas']['com.moego.server.payment.web.dto.PayoutView'][];
        platform: string;
        /** Format: int32 */
        size: number;
        /** Format: int64 */
        startCreatedDate: string | number;
      };
      'com.moego.server.payment.web.dto.PlatformCareRecordByCodeResp': {
        /** Format: int64 */
        accountId: string | number;
        /** Format: int64 */
        agreementId: string | number;
        agreementRecordUuid: string;
        code: string;
        /** Format: int64 */
        companyId: string | number;
        /** Format: date-time */
        createTime: string;
        discountCode: string;
        email: string;
        /** Format: int64 */
        id: string | number;
        orderShippingStatus: string;
        showAccounting: boolean;
        /** Format: date-time */
        signedTime: string;
        /** Format: int32 */
        status: number;
        /** Format: date-time */
        updateTime: string;
      };
      'com.moego.server.payment.web.dto.PlatformSalesAgreementByCodeResp': {
        agreementContent: string;
        agreementTitle: string;
        /** Format: int64 */
        id: string | number;
      };
      'com.moego.server.payment.web.dto.PlatformSalesRecordByCodeResp': {
        /** Format: int64 */
        accountId: string | number;
        /** Format: int64 */
        agreementId: string | number;
        agreementRecordUuid: string;
        code: string;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        companyType: number;
        /** Format: date-time */
        createdAt: string;
        email: string;
        hardwareCode: string;
        /** Format: int32 */
        hardwareDiscount: number;
        /** Format: int64 */
        id: string | number;
        isBdPlan: boolean;
        isCustomRate: boolean;
        /** Format: int32 */
        locationNum: number;
        minMonthlyTransaction: string;
        /** Format: int32 */
        needHardware: number;
        nonTerminalCardRate: string;
        orderShippingStatus: string;
        /** Format: int32 */
        premiumType: number;
        showAccounting: boolean;
        showAnnuallyTerm: boolean;
        showHardware: boolean;
        showMonthlyTerm: boolean;
        /** Format: date-time */
        signedTime: string;
        /** Format: int32 */
        status: number;
        subCouponCode: string;
        /** Format: int32 */
        subCouponValidMonth: number;
        /** Format: int32 */
        subDiscount: number;
        terminalCardRate: string;
        /** Format: date-time */
        updatedAt: string;
        /** Format: int32 */
        vansNum: number;
      };
      'com.moego.server.payment.web.dto.PreAuthPayResp': {
        /** Format: int32 */
        paymentId: number;
      };
      'com.moego.server.payment.web.dto.SignAgreementRecordByCodeResp': {
        agreementContent: string;
        /** Format: int64 */
        agreementId: string | number;
        agreementTitle: string;
        /** Format: int64 */
        companyId: string | number;
        signature: string;
        /** Format: date-time */
        signedTime: string;
      };
      /** @description stripe connected account info */
      'com.moego.server.payment.web.dto.StripeConnectedInfo': {
        /** @description Funds that are available to be transferred or paid outhttps://stripe.com/docs/api/balance/balance_object#balance_object-available */
        availableBalance: number;
        currency: string;
        /** @description Funds that are not yet available in the balance, due to the 7-day rolling pay cycle. */
        pendingBalance: number;
      };
      'com.moego.server.payment.web.dto.SubscriptionDTO': {
        clientSecret: string;
        subscriptionId: string;
      };
      'com.moego.server.payment.web.dto.UpdatePlatformCareRecordDTO': {
        /** Format: int64 */
        accountId: string | number;
        /** Format: int64 */
        agreementId: string | number;
        /** Format: int64 */
        businessId: string | number;
        /** Format: int64 */
        companyId: string | number;
        email: string;
        signature: string;
      };
      'com.moego.server.payment.web.dto.UpdatePlatformSalesRecordDTO': {
        /** Format: int64 */
        accountId: string | number;
        /** Format: int64 */
        agreementId: string | number;
        /** Format: int64 */
        businessId: string | number;
        /** Format: int64 */
        companyId: string | number;
        email: string;
        signature: string;
      };
      'com.moego.server.payment.web.dto.UpdatePlatformSalesStatusDTO': {
        code: string;
        /**
         * Format: int32
         * @description 0 Link not open 1 Link opened but not subscribe 2 subscribed monthly 3 subscribed annually
         */
        status: number;
      };
      'com.moego.server.payment.web.param.OnlineBookingPayParams': {
        /** @description 是否在支付金额加入processing fee pay by client */
        addProcessingFee?: boolean;
        amount: number;
        cardNumber?: string;
        cardType?: string;
        /** @description for credit card */
        chargeToken?: string;
        checkNumber?: string;
        customerData?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        /** Format: int32 */
        customerId: number;
        description?: string;
        discountCode?: components['schemas']['com.moego.server.payment.web.param.OnlineBookingPayParams$DiscountCode'];
        expMonth?: string;
        expYear?: string;
        guid?: string;
        /** Format: int32 */
        isDeposit?: number;
        isOnline?: boolean;
        /** @description set when Stripe reader/terminal calling */
        locationId?: string;
        method?: string;
        /** Format: int32 */
        methodId?: number;
        module?: string;
        /** Format: int64 */
        orderId: string | number;
        paidBy?: string;
        paymentIntentId?: string;
        prepay?: components['schemas']['com.moego.server.payment.web.param.OnlineBookingPayParams$Prepay'];
        readerId?: string;
        saveCard?: boolean;
        signature?: string;
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        stripePaymentMethod?: number;
        stripePaymentMethodId?: string;
        /** @description tips amount */
        tipsAmount?: number;
      };
      'com.moego.server.payment.web.param.OnlineBookingPayParams$DiscountCode': {
        discountAmount: number;
        /** Format: int64 */
        discountCodeId: string | number;
      };
      'com.moego.server.payment.web.param.OnlineBookingPayParams$Prepay': {
        serviceChargeAmount: number;
        serviceTotal: number;
        taxAmount: number;
      };
      'com.moego.server.payment.web.vo.ACHQueryVo': {
        /** Format: int32 */
        paymentId: number;
      };
      'com.moego.server.payment.web.vo.ACHValidateCodeVo': {
        /** Format: int32 */
        paymentId: number;
        verifyCode: string;
        verifyType?: string;
      };
      'com.moego.server.payment.web.vo.CardAuthenticationReq': {
        paymentMethods: string[];
        stripeCustomerId: string;
      };
      'com.moego.server.payment.web.vo.CreateACHPaymentVo': {
        achInfo?: components['schemas']['com.moego.server.payment.web.dto.ACHDTO'];
        /** @description 是否在支付金额加入processing fee pay by client */
        addProcessingFee?: boolean;
        amount: number;
        amountWithoutBookingFee?: number;
        amountWithoutTips?: number;
        /** @deprecated */
        bookingFeeAmount?: number;
        /** @description 支付请求的来源是OB时需要传的businessName */
        businessName?: string;
        cardNumber?: string;
        cardType?: string;
        /** @description for credit card */
        chargeToken?: string;
        checkNumber?: string;
        customerData?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        /** Format: int32 */
        customerId?: number;
        description?: string;
        expMonth?: string;
        expYear?: string;
        fromPreAuth?: boolean;
        guid?: string;
        /** Format: int32 */
        invoiceId: number;
        isCancellationFee?: boolean;
        /** Format: int32 */
        isDeposit?: number;
        isOnline?: boolean;
        /** @description set when Stripe reader/terminal calling */
        locationId?: string;
        method?: string;
        /** Format: int32 */
        methodId?: number;
        module?: string;
        paidBy?: string;
        paymentIntentId?: string;
        /** Format: int32 */
        preAuthId?: number;
        /** Format: int64 */
        rawCreateTime?: string | number;
        rawId?: string;
        /** Format: int64 */
        rawUpdateTime?: string | number;
        readerId?: string;
        saveCard?: boolean;
        signature?: string;
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        stripePaymentMethod?: number;
        stripePaymentMethodId?: string;
        /** @description tips amount */
        tipsAmount?: number;
      };
      'com.moego.server.payment.web.vo.DisputeQueryVO': {
        endDate: string;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        startDate: string;
        status?: string[];
      };
      'com.moego.server.payment.web.vo.DisputeReadStatusVO': {
        hasUnread: boolean;
      };
      /** @description export payout history data object */
      'com.moego.server.payment.web.vo.PayoutDetailsExportReq': {
        /** @description 文件名 */
        fileName?: string;
        /** @description 文件类型 */
        fileType?: string;
        /** @description payoutId */
        payoutId: string;
      };
      'com.moego.server.payment.web.vo.PayoutListSummaryVo': {
        endCreatedDate: string;
        startCreatedDate: string;
      };
      'com.moego.server.payment.web.vo.PayoutReq': {
        /**
         * Format: int64
         * @description T+n payout设置中，n取 delayDays 的值
         */
        delayDays: string | number;
        /** @deprecated */
        inWhiteList: boolean;
      };
      'com.moego.server.payment.web.vo.PreAuthPayVo': {
        addProcessingFee: boolean;
        amount: number;
        description?: string;
        /** Format: int32 */
        invoiceId: number;
        paidBy?: string;
        /** Format: int32 */
        preAuthId: number;
        tipsAmount?: number;
      };
      'com.moego.server.payment.web.vo.PreAuthSwitchVo': {
        preAuthCardNumber?: string;
        preAuthEnable: boolean;
        preAuthPaymentMethod?: string;
        /** Format: int32 */
        ticketId: number;
      };
      'com.moego.server.payment.web.vo.ReaderCancelReq': {
        readerId: string;
      };
      'com.moego.server.payment.web.vo.ReaderProcessReq': {
        paymentIntentId: string;
        readerId: string;
        /** Format: int64 */
        tipEligibleAmount?: string | number;
      };
      'com.moego.server.payment.web.vo.StripeDisputeVo': {
        /** Format: int64 */
        amount: string | number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        chargedOn: string | number;
        /** Format: date-time */
        createdAt: string;
        currency: string;
        customer: string;
        /** Format: int64 */
        disputedOn: string | number;
        disputeId: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        paymentId: number;
        paymentIntentId: string;
        paymentMethod: string;
        reason: string;
        /** Format: int64 */
        respondedOn: string | number;
        status: string;
        /** Format: date-time */
        updatedAt: string;
      };
      'com.moego.server.payment.web.vo.SubmitInvoiceRefundVo': {
        /** Format: int32 */
        invoiceId: number;
        refundAmount: number;
        refundReason?: string;
        refunds: components['schemas']['com.moego.server.payment.dto.CanRefundChannel'][];
      };
      'com.moego.server.payment.web.vo.SubscriptionOrderVo': {
        /** Format: int32 */
        companyId: number;
        /**
         * Format: int64
         * @description create time timezone utc
         */
        createTime: string | number;
        /** Format: int32 */
        id: number;
        /**
         * Format: int32
         * @description account id
         */
        operatorId: number;
        /** Format: int32 */
        planId: number;
        subscriptionId: string;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.payment.web.vo.TestClockVO': {
        /** Format: int64 */
        created: string | number;
        /** Format: int64 */
        frozenTime: string | number;
        id: string;
        livemode: boolean;
        name: string;
        object: string;
        status: string;
      };
      /** @description export transaction history data object */
      'com.moego.server.payment.web.vo.TransactionHistoryReq': {
        /** Format: int32 */
        customerId: number;
        /** @description not null yyyy-MM-dd */
        endDate: string;
        /** @description 文件名 */
        fileName: string;
        /** @description 文件类型 */
        fileType: string;
        invoiceIds: number[];
        method: string;
        module: string;
        order: string;
        /**
         * Format: int32
         * @description 导出时填null
         */
        pageNum: number;
        /**
         * Format: int32
         * @description 导出时填null
         */
        pageSize: number;
        /**
         * Format: int32
         * @description account structure,use for searching specific business
         */
        searchBusinessId: number;
        /** @description not null yyyy-MM-dd */
        startDate: string;
        /**
         * Format: int64
         * @description only used for bulk payment: get all payment records of one payment transaction
         */
        transactionId: string | number;
        vendor: string;
      };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
  }
}
export enum PathsPaymentStripeGetPaymentMethodListGetResponses200Content__stripe__ {
  com_stripe_model_PaymentMethodCollection = 'com.stripe.model.PaymentMethodCollection',
}
export enum PathsPaymentSubscriptionEnterpriseSubscriptionRetrieveGetResponses200Content__stripe__ {
  com_stripe_model_Subscription = 'com.stripe.model.Subscription',
}
export enum PathsPaymentSubscriptionSubscriptionRetrieveGetResponses200Content__stripe__ {
  com_stripe_model_Subscription = 'com.stripe.model.Subscription',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelAccountData__stripe__ {
  com_stripe_model_Account = 'com.stripe.model.Account',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelAccountLinkData__stripe__ {
  com_stripe_model_AccountLink = 'com.stripe.model.AccountLink',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelBalanceData__stripe__ {
  com_stripe_model_Balance = 'com.stripe.model.Balance',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelCustomerData__stripe__ {
  com_stripe_model_Customer = 'com.stripe.model.Customer',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelDisputeCollectionData__stripe__ {
  com_stripe_model_DisputeCollection = 'com.stripe.model.DisputeCollection',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelExternalAccountData__stripe__ {
  com_stripe_model_ExternalAccount = 'com.stripe.model.ExternalAccount',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelPayoutCollectionData__stripe__ {
  com_stripe_model_PayoutCollection = 'com.stripe.model.PayoutCollection',
}
export enum ComMoegoCommonResponseResponseResultComStripeModelPersonCollectionData__stripe__ {
  com_stripe_model_PersonCollection = 'com.stripe.model.PersonCollection',
}
export enum ComMoegoServerCustomerDtoCustomerNotificationSettingDTOMarketingCampaignNotifications {
  SMS = 'SMS',
  EMAIL = 'EMAIL',
  AutoCall = 'AutoCall',
}
export enum ComMoegoServerCustomerDtoCustomerNotificationSettingDTOServiceRelatedNotifications {
  SMS = 'SMS',
  EMAIL = 'EMAIL',
  AutoCall = 'AutoCall',
}
export enum ComMoegoServerPaymentDtoCouponDTOBusinessCategory {
  UNSPECIFIED = 'UNSPECIFIED',
  PLATFORM_CARE = 'PLATFORM_CARE',
  PAY_ADMIN = 'PAY_ADMIN',
  REFERRAL = 'REFERRAL',
  CARE = 'CARE',
  HARDWARE = 'HARDWARE',
  PLATFORM_SALES = 'PLATFORM_SALES',
}
export enum ComMoegoServerPaymentDtoStripeHardwareOrderParamsBundleSaleType {
  UNIQUE_LINK = 'UNIQUE_LINK',
  SALE = 'SALE',
  PLATFORM_SALES = 'PLATFORM_SALES',
}
export enum ComMoegoServerPaymentDtoStripeLocationDtoAddress__stripe__ {
  com_stripe_model_Address = 'com.stripe.model.Address',
}
export enum ComMoegoServerPaymentWebDtoPayoutRespPayoutScheduleStatus {
  INIT = 'INIT',
  NOT_SUPPORTED_COUNTRY = 'NOT_SUPPORTED_COUNTRY',
  NOT_AVAILABLE = 'NOT_AVAILABLE',
  AVAILABLE = 'AVAILABLE',
  ENABLED = 'ENABLED',
  REVIEWING = 'REVIEWING',
  REVIEW_FAILED = 'REVIEW_FAILED',
}
