/* This file is generated by @moego/openapi2dts, please do not edit it. */
import { business } from './business-schema';
import { customer } from './customer-schema';
import { grooming } from './grooming-schema';
import { message } from './message-schema';
import { payment } from './payment-schema';
import { retail } from './retail-schema';

export interface OpenApiDefinitions {
  customer: customer.components['schemas'];
  business: business.components['schemas'];
  message: message.components['schemas'];
  payment: payment.components['schemas'];
  grooming: grooming.components['schemas'];
  retail: retail.components['schemas'];
}

export interface OpenApiModels
  extends customer.paths,
    business.paths,
    message.paths,
    payment.paths,
    grooming.paths,
    retail.paths {}
