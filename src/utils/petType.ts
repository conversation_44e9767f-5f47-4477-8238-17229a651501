import { ComMoegoServerGroomingWebDtoObInfoDtoAvailablePetTypes as AvailablePetTypes } from 'types/openApi/grooming-schema';

export interface PetTypeOption {
  petTypeId: number;
  typeName: string;
  bookOnlineAvailable: number;
}

export const petTypeEnumMap: Record<AvailablePetTypes, PetTypeOption> = {
  [AvailablePetTypes.PET_TYPE_UNSPECIFIED]: { petTypeId: 0, typeName: 'Unspecified', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_DOG]: { petTypeId: 1, typeName: 'Dog', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_CAT]: { petTypeId: 2, typeName: 'Cat', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_BIRD]: { petTypeId: 3, typeName: 'Bird', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_RABBIT]: { petTypeId: 4, typeName: 'Rabbit', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_GUINEA_PIG]: { petTypeId: 5, typeName: 'Guinea pig', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_HORSE]: { petTypeId: 6, typeName: 'Horse', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_RAT]: { petTypeId: 7, typeName: 'Rat', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_MOUSE]: { petTypeId: 8, typeName: 'Mouse', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_HAMSTER]: { petTypeId: 9, typeName: 'Hamster', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_CHINCHILLA]: { petTypeId: 10, typeName: 'Chinchilla', bookOnlineAvailable: 1 },
  [AvailablePetTypes.PET_TYPE_OTHER]: { petTypeId: 11, typeName: 'Other', bookOnlineAvailable: 1 },
  [AvailablePetTypes.UNRECOGNIZED]: { petTypeId: 0, typeName: 'Unspecified', bookOnlineAvailable: 1 },
};

export const getPetTypeOptionByEnum = (petTypeEnum: AvailablePetTypes) => {
  return petTypeEnumMap[petTypeEnum];
};
